{"swagger": "2.0", "info": {"title": "Master Internal", "description": "Master Internal API", "version": "1.0.0", "contact": {"name": "Jony4", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "127.0.0.1:8962", "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/devices": {"post": {"summary": "DeviceRegister registers a device.\nDeviceRegister 注册一个设备。", "operationId": "MasterInternalService_DeviceRegister", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/master_internalDevice"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "tags": ["MasterInternalService"]}}, "/api/devices/{device_id}/ping": {"post": {"summary": "DevicePing pings a device.\nDevicePing 对设备进行 ping 操作。", "operationId": "MasterInternalService_DevicePing", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/master_internalDevice"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "device_id", "in": "path", "required": true, "type": "string"}], "tags": ["MasterInternalService"]}}, "/api/medias/{id}": {"put": {"summary": "MediaUpdate updates a media.\nMediaUpdate 更新一个媒体。", "operationId": "MasterInternalService_MediaUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/master_internalMedia"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["MasterInternalService"]}}, "/api/tasks/receive": {"post": {"summary": "领取任务\nReceive a task.", "operationId": "MasterInternalService_TaskReceive", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/master_internalTaskReceiveResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "tags": ["MasterInternalService"]}}, "/api/tasks/{id}/status": {"post": {"summary": "TaskUpdateStatus updates the status of a task.\nTaskUpdateStatus 更新任务的状态。", "operationId": "MasterInternalService_TaskUpdateStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/master_internalTaskUpdateStatusResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["MasterInternalService"]}}}, "definitions": {"master_internalDevice": {"type": "object", "properties": {"name": {"type": "string"}, "device_id": {"type": "string"}, "online": {"type": "boolean"}, "last_ping_time": {"type": "string", "format": "int64"}}, "title": "Device defines the structure of a device.\nDevice 定义了设备的结构。"}, "master_internalMedia": {"type": "object", "properties": {"id": {"type": "string"}, "media_name": {"type": "string"}, "type": {"$ref": "#/definitions/master_internalMediaType"}, "source_url": {"type": "string"}, "transcription": {"type": "string"}, "lrc": {"type": "string"}, "vtt": {"type": "string"}, "graph": {"type": "string"}, "keywords": {"type": "string"}}, "title": "Media defines the structure of a media.\nMedia 定义了媒体的结构。"}, "master_internalMediaType": {"type": "string", "enum": ["MediaTypeVIDEO", "MediaTypeAUDIO", "MediaTypeIMAGE", "MediaTypeTEXT", "MediaTypePDF", "MediaTypeDOC", "MediaTypePPT", "MediaTypeXLS", "MediaTypeHTML", "MediaTypeMarkdown", "MediaTypeCSV", "MediaTypeEml"], "default": "MediaTypeVIDEO"}, "master_internalPresetQuestion": {"type": "object", "properties": {"id": {"type": "string"}, "question": {"type": "string"}, "prompt": {"type": "string"}}, "title": "PresetQuestion defines the structure of a preset question.\nPresetQuestion 定义了预设问题的结构。"}, "master_internalTask": {"type": "object", "properties": {"id": {"type": "string"}, "task_name": {"type": "string"}, "task_type": {"type": "string"}, "device_id": {"type": "string"}, "status": {"type": "string"}, "media_id": {"type": "string"}, "preset_question_ids": {"type": "string"}, "start_time": {"type": "string", "format": "int64"}, "end_time": {"type": "string", "format": "int64"}, "status_desc": {"type": "string"}, "model_name": {"type": "string"}, "preset_questions": {"type": "object", "additionalProperties": {"$ref": "#/definitions/master_internalPresetQuestion"}}}}, "master_internalTaskReceiveResponse": {"type": "object", "properties": {"task": {"$ref": "#/definitions/master_internalTask"}}}, "master_internalTaskStatus": {"type": "string", "enum": ["TaskStatusINIT", "TaskStatusRUNNING", "TaskStatusFINNISH", "TaskStatusERROR"], "default": "TaskStatusINIT"}, "master_internalTaskUpdateStatusResponse": {"type": "object", "title": "TaskStatusUpdateResponse is the response structure for updating the status of a task.\nTaskUpdateStatusResponse 是更新任务状态的响应结构。"}, "protobufAny": {"type": "object", "properties": {"type_url": {"type": "string"}, "value": {"type": "string", "format": "byte"}}}, "runtimeError": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}