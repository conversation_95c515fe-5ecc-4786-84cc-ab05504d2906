// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: master_internal/master_internal.proto

package master_internal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MasterInternalService_TaskUpdateStatus_FullMethodName = "/jony4.projects.jnqx.video.extract.master_internal.MasterInternalService/TaskUpdateStatus"
	MasterInternalService_TaskReceive_FullMethodName      = "/jony4.projects.jnqx.video.extract.master_internal.MasterInternalService/TaskReceive"
	MasterInternalService_MediaUpdate_FullMethodName      = "/jony4.projects.jnqx.video.extract.master_internal.MasterInternalService/MediaUpdate"
	MasterInternalService_DeviceRegister_FullMethodName   = "/jony4.projects.jnqx.video.extract.master_internal.MasterInternalService/DeviceRegister"
	MasterInternalService_DevicePing_FullMethodName       = "/jony4.projects.jnqx.video.extract.master_internal.MasterInternalService/DevicePing"
)

// MasterInternalServiceClient is the client API for MasterInternalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MasterService defines the service that handles tasks, media, preset questions, and media question answers.
// MasterService 定义了处理任务，媒体，预设问题和媒体问题答案的服务。
type MasterInternalServiceClient interface {
	// TaskUpdateStatus updates the status of a task.
	// TaskUpdateStatus 更新任务的状态。
	TaskUpdateStatus(ctx context.Context, in *TaskUpdateStatusRequest, opts ...grpc.CallOption) (*TaskUpdateStatusResponse, error)
	// 领取任务
	// Receive a task.
	TaskReceive(ctx context.Context, in *TaskReceiveRequest, opts ...grpc.CallOption) (*TaskReceiveResponse, error)
	// MediaUpdate updates a media.
	// MediaUpdate 更新一个媒体。
	MediaUpdate(ctx context.Context, in *Media, opts ...grpc.CallOption) (*Media, error)
	// DeviceRegister registers a device.
	// DeviceRegister 注册一个设备。
	DeviceRegister(ctx context.Context, in *Device, opts ...grpc.CallOption) (*Device, error)
	// DevicePing pings a device.
	// DevicePing 对设备进行 ping 操作。
	DevicePing(ctx context.Context, in *Device, opts ...grpc.CallOption) (*Device, error)
}

type masterInternalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMasterInternalServiceClient(cc grpc.ClientConnInterface) MasterInternalServiceClient {
	return &masterInternalServiceClient{cc}
}

func (c *masterInternalServiceClient) TaskUpdateStatus(ctx context.Context, in *TaskUpdateStatusRequest, opts ...grpc.CallOption) (*TaskUpdateStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskUpdateStatusResponse)
	err := c.cc.Invoke(ctx, MasterInternalService_TaskUpdateStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterInternalServiceClient) TaskReceive(ctx context.Context, in *TaskReceiveRequest, opts ...grpc.CallOption) (*TaskReceiveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskReceiveResponse)
	err := c.cc.Invoke(ctx, MasterInternalService_TaskReceive_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterInternalServiceClient) MediaUpdate(ctx context.Context, in *Media, opts ...grpc.CallOption) (*Media, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Media)
	err := c.cc.Invoke(ctx, MasterInternalService_MediaUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterInternalServiceClient) DeviceRegister(ctx context.Context, in *Device, opts ...grpc.CallOption) (*Device, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Device)
	err := c.cc.Invoke(ctx, MasterInternalService_DeviceRegister_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterInternalServiceClient) DevicePing(ctx context.Context, in *Device, opts ...grpc.CallOption) (*Device, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Device)
	err := c.cc.Invoke(ctx, MasterInternalService_DevicePing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MasterInternalServiceServer is the server API for MasterInternalService service.
// All implementations should embed UnimplementedMasterInternalServiceServer
// for forward compatibility.
//
// MasterService defines the service that handles tasks, media, preset questions, and media question answers.
// MasterService 定义了处理任务，媒体，预设问题和媒体问题答案的服务。
type MasterInternalServiceServer interface {
	// TaskUpdateStatus updates the status of a task.
	// TaskUpdateStatus 更新任务的状态。
	TaskUpdateStatus(context.Context, *TaskUpdateStatusRequest) (*TaskUpdateStatusResponse, error)
	// 领取任务
	// Receive a task.
	TaskReceive(context.Context, *TaskReceiveRequest) (*TaskReceiveResponse, error)
	// MediaUpdate updates a media.
	// MediaUpdate 更新一个媒体。
	MediaUpdate(context.Context, *Media) (*Media, error)
	// DeviceRegister registers a device.
	// DeviceRegister 注册一个设备。
	DeviceRegister(context.Context, *Device) (*Device, error)
	// DevicePing pings a device.
	// DevicePing 对设备进行 ping 操作。
	DevicePing(context.Context, *Device) (*Device, error)
}

// UnimplementedMasterInternalServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMasterInternalServiceServer struct{}

func (UnimplementedMasterInternalServiceServer) TaskUpdateStatus(context.Context, *TaskUpdateStatusRequest) (*TaskUpdateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskUpdateStatus not implemented")
}
func (UnimplementedMasterInternalServiceServer) TaskReceive(context.Context, *TaskReceiveRequest) (*TaskReceiveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskReceive not implemented")
}
func (UnimplementedMasterInternalServiceServer) MediaUpdate(context.Context, *Media) (*Media, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MediaUpdate not implemented")
}
func (UnimplementedMasterInternalServiceServer) DeviceRegister(context.Context, *Device) (*Device, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceRegister not implemented")
}
func (UnimplementedMasterInternalServiceServer) DevicePing(context.Context, *Device) (*Device, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevicePing not implemented")
}
func (UnimplementedMasterInternalServiceServer) testEmbeddedByValue() {}

// UnsafeMasterInternalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MasterInternalServiceServer will
// result in compilation errors.
type UnsafeMasterInternalServiceServer interface {
	mustEmbedUnimplementedMasterInternalServiceServer()
}

func RegisterMasterInternalServiceServer(s grpc.ServiceRegistrar, srv MasterInternalServiceServer) {
	// If the following call pancis, it indicates UnimplementedMasterInternalServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MasterInternalService_ServiceDesc, srv)
}

func _MasterInternalService_TaskUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskUpdateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterInternalServiceServer).TaskUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterInternalService_TaskUpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterInternalServiceServer).TaskUpdateStatus(ctx, req.(*TaskUpdateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterInternalService_TaskReceive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskReceiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterInternalServiceServer).TaskReceive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterInternalService_TaskReceive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterInternalServiceServer).TaskReceive(ctx, req.(*TaskReceiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterInternalService_MediaUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Media)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterInternalServiceServer).MediaUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterInternalService_MediaUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterInternalServiceServer).MediaUpdate(ctx, req.(*Media))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterInternalService_DeviceRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Device)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterInternalServiceServer).DeviceRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterInternalService_DeviceRegister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterInternalServiceServer).DeviceRegister(ctx, req.(*Device))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterInternalService_DevicePing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Device)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterInternalServiceServer).DevicePing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterInternalService_DevicePing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterInternalServiceServer).DevicePing(ctx, req.(*Device))
	}
	return interceptor(ctx, in, info, handler)
}

// MasterInternalService_ServiceDesc is the grpc.ServiceDesc for MasterInternalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MasterInternalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "jony4.projects.jnqx.video.extract.master_internal.MasterInternalService",
	HandlerType: (*MasterInternalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TaskUpdateStatus",
			Handler:    _MasterInternalService_TaskUpdateStatus_Handler,
		},
		{
			MethodName: "TaskReceive",
			Handler:    _MasterInternalService_TaskReceive_Handler,
		},
		{
			MethodName: "MediaUpdate",
			Handler:    _MasterInternalService_MediaUpdate_Handler,
		},
		{
			MethodName: "DeviceRegister",
			Handler:    _MasterInternalService_DeviceRegister_Handler,
		},
		{
			MethodName: "DevicePing",
			Handler:    _MasterInternalService_DevicePing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "master_internal/master_internal.proto",
}
