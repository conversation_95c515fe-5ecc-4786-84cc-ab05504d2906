// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: master_internal/master_internal.proto

package master_internal

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MediaType int32

const (
	MediaType_MediaTypeVIDEO    MediaType = 0
	MediaType_MediaTypeAUDIO    MediaType = 1
	MediaType_MediaTypeIMAGE    MediaType = 2
	MediaType_MediaTypeTEXT     MediaType = 3
	MediaType_MediaTypePDF      MediaType = 4
	MediaType_MediaTypeDOC      MediaType = 5
	MediaType_MediaTypePPT      MediaType = 6
	MediaType_MediaTypeXLS      MediaType = 7
	MediaType_MediaTypeHTML     MediaType = 8
	MediaType_MediaTypeMarkdown MediaType = 9
	MediaType_MediaTypeCSV      MediaType = 10
	MediaType_MediaTypeEml      MediaType = 11
)

// Enum value maps for MediaType.
var (
	MediaType_name = map[int32]string{
		0:  "MediaTypeVIDEO",
		1:  "MediaTypeAUDIO",
		2:  "MediaTypeIMAGE",
		3:  "MediaTypeTEXT",
		4:  "MediaTypePDF",
		5:  "MediaTypeDOC",
		6:  "MediaTypePPT",
		7:  "MediaTypeXLS",
		8:  "MediaTypeHTML",
		9:  "MediaTypeMarkdown",
		10: "MediaTypeCSV",
		11: "MediaTypeEml",
	}
	MediaType_value = map[string]int32{
		"MediaTypeVIDEO":    0,
		"MediaTypeAUDIO":    1,
		"MediaTypeIMAGE":    2,
		"MediaTypeTEXT":     3,
		"MediaTypePDF":      4,
		"MediaTypeDOC":      5,
		"MediaTypePPT":      6,
		"MediaTypeXLS":      7,
		"MediaTypeHTML":     8,
		"MediaTypeMarkdown": 9,
		"MediaTypeCSV":      10,
		"MediaTypeEml":      11,
	}
)

func (x MediaType) Enum() *MediaType {
	p := new(MediaType)
	*p = x
	return p
}

func (x MediaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediaType) Descriptor() protoreflect.EnumDescriptor {
	return file_master_internal_master_internal_proto_enumTypes[0].Descriptor()
}

func (MediaType) Type() protoreflect.EnumType {
	return &file_master_internal_master_internal_proto_enumTypes[0]
}

func (x MediaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MediaType.Descriptor instead.
func (MediaType) EnumDescriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{0}
}

type TaskStatus int32

const (
	TaskStatus_TaskStatusINIT    TaskStatus = 0
	TaskStatus_TaskStatusRUNNING TaskStatus = 1
	TaskStatus_TaskStatusFINNISH TaskStatus = 2
	TaskStatus_TaskStatusERROR   TaskStatus = 3
)

// Enum value maps for TaskStatus.
var (
	TaskStatus_name = map[int32]string{
		0: "TaskStatusINIT",
		1: "TaskStatusRUNNING",
		2: "TaskStatusFINNISH",
		3: "TaskStatusERROR",
	}
	TaskStatus_value = map[string]int32{
		"TaskStatusINIT":    0,
		"TaskStatusRUNNING": 1,
		"TaskStatusFINNISH": 2,
		"TaskStatusERROR":   3,
	}
)

func (x TaskStatus) Enum() *TaskStatus {
	p := new(TaskStatus)
	*p = x
	return p
}

func (x TaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_master_internal_master_internal_proto_enumTypes[1].Descriptor()
}

func (TaskStatus) Type() protoreflect.EnumType {
	return &file_master_internal_master_internal_proto_enumTypes[1]
}

func (x TaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus.Descriptor instead.
func (TaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{1}
}

// Media defines the structure of a media.
// Media 定义了媒体的结构。
type Media struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	MediaName     string                 `protobuf:"bytes,1,opt,name=media_name,json=mediaName,proto3" json:"media_name,omitempty"`
	Type          MediaType              `protobuf:"varint,2,opt,name=type,proto3,enum=jony4.projects.jnqx.video.extract.master_internal.MediaType" json:"type,omitempty"`
	SourceUrl     string                 `protobuf:"bytes,7,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	Transcription string                 `protobuf:"bytes,20,opt,name=transcription,proto3" json:"transcription,omitempty"`
	Lrc           string                 `protobuf:"bytes,21,opt,name=lrc,proto3" json:"lrc,omitempty"`
	Vtt           string                 `protobuf:"bytes,23,opt,name=vtt,proto3" json:"vtt,omitempty"`
	Graph         string                 `protobuf:"bytes,25,opt,name=graph,proto3" json:"graph,omitempty"`
	Keywords      string                 `protobuf:"bytes,26,opt,name=keywords,proto3" json:"keywords,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Media) Reset() {
	*x = Media{}
	mi := &file_master_internal_master_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Media) ProtoMessage() {}

func (x *Media) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Media.ProtoReflect.Descriptor instead.
func (*Media) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{0}
}

func (x *Media) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Media) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *Media) GetType() MediaType {
	if x != nil {
		return x.Type
	}
	return MediaType_MediaTypeVIDEO
}

func (x *Media) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *Media) GetTranscription() string {
	if x != nil {
		return x.Transcription
	}
	return ""
}

func (x *Media) GetLrc() string {
	if x != nil {
		return x.Lrc
	}
	return ""
}

func (x *Media) GetVtt() string {
	if x != nil {
		return x.Vtt
	}
	return ""
}

func (x *Media) GetGraph() string {
	if x != nil {
		return x.Graph
	}
	return ""
}

func (x *Media) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

// PresetQuestion defines the structure of a preset question.
// PresetQuestion 定义了预设问题的结构。
type PresetQuestion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	Question      string                 `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Prompt        string                 `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetQuestion) Reset() {
	*x = PresetQuestion{}
	mi := &file_master_internal_master_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetQuestion) ProtoMessage() {}

func (x *PresetQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetQuestion.ProtoReflect.Descriptor instead.
func (*PresetQuestion) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{1}
}

func (x *PresetQuestion) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PresetQuestion) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *PresetQuestion) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

// MediaQuestionAnswer defines the structure of a media question answer.
// TaskQuestionAnswer 定义了媒体问题答案的结构。
type TaskQuestionAnswer struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TaskId           string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	MediaId          string                 `protobuf:"bytes,2,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	PresetQuestionId string                 `protobuf:"bytes,3,opt,name=preset_question_id,json=presetQuestionId,proto3" json:"preset_question_id,omitempty"`
	Question         string                 `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	Answer           string                 `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TaskQuestionAnswer) Reset() {
	*x = TaskQuestionAnswer{}
	mi := &file_master_internal_master_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskQuestionAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskQuestionAnswer) ProtoMessage() {}

func (x *TaskQuestionAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskQuestionAnswer.ProtoReflect.Descriptor instead.
func (*TaskQuestionAnswer) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{2}
}

func (x *TaskQuestionAnswer) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskQuestionAnswer) GetMediaId() string {
	if x != nil {
		return x.MediaId
	}
	return ""
}

func (x *TaskQuestionAnswer) GetPresetQuestionId() string {
	if x != nil {
		return x.PresetQuestionId
	}
	return ""
}

func (x *TaskQuestionAnswer) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *TaskQuestionAnswer) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

type MediaQuestionAnswersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Answers       []*TaskQuestionAnswer  `protobuf:"bytes,1,rep,name=answers,proto3" json:"answers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaQuestionAnswersResponse) Reset() {
	*x = MediaQuestionAnswersResponse{}
	mi := &file_master_internal_master_internal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaQuestionAnswersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaQuestionAnswersResponse) ProtoMessage() {}

func (x *MediaQuestionAnswersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaQuestionAnswersResponse.ProtoReflect.Descriptor instead.
func (*MediaQuestionAnswersResponse) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{3}
}

func (x *MediaQuestionAnswersResponse) GetAnswers() []*TaskQuestionAnswer {
	if x != nil {
		return x.Answers
	}
	return nil
}

// Device defines the structure of a device.
// Device 定义了设备的结构。
type Device struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Online        bool                   `protobuf:"varint,3,opt,name=online,proto3" json:"online,omitempty"`
	LastPingTime  int64                  `protobuf:"varint,4,opt,name=last_ping_time,json=lastPingTime,proto3" json:"last_ping_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_master_internal_master_internal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{4}
}

func (x *Device) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Device) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Device) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *Device) GetLastPingTime() int64 {
	if x != nil {
		return x.LastPingTime
	}
	return 0
}

// TaskStatusUpdateRequest is the request structure for updating the status of a task.
// TaskUpdateStatusRequest 是更新任务状态的请求结构。
type TaskUpdateStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status        TaskStatus             `protobuf:"varint,2,opt,name=status,proto3,enum=jony4.projects.jnqx.video.extract.master_internal.TaskStatus" json:"status,omitempty"`
	StatusDesc    string                 `protobuf:"bytes,3,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskUpdateStatusRequest) Reset() {
	*x = TaskUpdateStatusRequest{}
	mi := &file_master_internal_master_internal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskUpdateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskUpdateStatusRequest) ProtoMessage() {}

func (x *TaskUpdateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskUpdateStatusRequest.ProtoReflect.Descriptor instead.
func (*TaskUpdateStatusRequest) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{5}
}

func (x *TaskUpdateStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskUpdateStatusRequest) GetStatus() TaskStatus {
	if x != nil {
		return x.Status
	}
	return TaskStatus_TaskStatusINIT
}

func (x *TaskUpdateStatusRequest) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

// TaskStatusUpdateResponse is the response structure for updating the status of a task.
// TaskUpdateStatusResponse 是更新任务状态的响应结构。
type TaskUpdateStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskUpdateStatusResponse) Reset() {
	*x = TaskUpdateStatusResponse{}
	mi := &file_master_internal_master_internal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskUpdateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskUpdateStatusResponse) ProtoMessage() {}

func (x *TaskUpdateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskUpdateStatusResponse.ProtoReflect.Descriptor instead.
func (*TaskUpdateStatusResponse) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{6}
}

type TaskReceiveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskReceiveRequest) Reset() {
	*x = TaskReceiveRequest{}
	mi := &file_master_internal_master_internal_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskReceiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskReceiveRequest) ProtoMessage() {}

func (x *TaskReceiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskReceiveRequest.ProtoReflect.Descriptor instead.
func (*TaskReceiveRequest) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{7}
}

func (x *TaskReceiveRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type TaskReceiveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Task          *Task                  `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskReceiveResponse) Reset() {
	*x = TaskReceiveResponse{}
	mi := &file_master_internal_master_internal_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskReceiveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskReceiveResponse) ProtoMessage() {}

func (x *TaskReceiveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskReceiveResponse.ProtoReflect.Descriptor instead.
func (*TaskReceiveResponse) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{8}
}

func (x *TaskReceiveResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

type Task struct {
	state             protoimpl.MessageState     `protogen:"open.v1"`
	Id                string                     `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	TaskName          string                     `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskType          string                     `protobuf:"bytes,2,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	DeviceId          string                     `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Status            string                     `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	MediaId           string                     `protobuf:"bytes,5,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	PresetQuestionIds string                     `protobuf:"bytes,6,opt,name=preset_question_ids,json=presetQuestionIds,proto3" json:"preset_question_ids,omitempty"`
	StartTime         int64                      `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime           int64                      `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	StatusDesc        string                     `protobuf:"bytes,12,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	ModelName         string                     `protobuf:"bytes,13,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	PresetQuestions   map[string]*PresetQuestion `protobuf:"bytes,20,rep,name=preset_questions,json=presetQuestions,proto3" json:"preset_questions,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_master_internal_master_internal_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{9}
}

func (x *Task) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Task) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *Task) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *Task) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Task) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Task) GetMediaId() string {
	if x != nil {
		return x.MediaId
	}
	return ""
}

func (x *Task) GetPresetQuestionIds() string {
	if x != nil {
		return x.PresetQuestionIds
	}
	return ""
}

func (x *Task) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Task) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Task) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *Task) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *Task) GetPresetQuestions() map[string]*PresetQuestion {
	if x != nil {
		return x.PresetQuestions
	}
	return nil
}

type MediaListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaListRequest) Reset() {
	*x = MediaListRequest{}
	mi := &file_master_internal_master_internal_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaListRequest) ProtoMessage() {}

func (x *MediaListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaListRequest.ProtoReflect.Descriptor instead.
func (*MediaListRequest) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{10}
}

type MediaListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Medias        []*Media               `protobuf:"bytes,1,rep,name=medias,proto3" json:"medias,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaListResponse) Reset() {
	*x = MediaListResponse{}
	mi := &file_master_internal_master_internal_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaListResponse) ProtoMessage() {}

func (x *MediaListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_internal_master_internal_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaListResponse.ProtoReflect.Descriptor instead.
func (*MediaListResponse) Descriptor() ([]byte, []int) {
	return file_master_internal_master_internal_proto_rawDescGZIP(), []int{11}
}

func (x *MediaListResponse) GetMedias() []*Media {
	if x != nil {
		return x.Medias
	}
	return nil
}

var File_master_internal_master_internal_proto protoreflect.FileDescriptor

const file_master_internal_master_internal_proto_rawDesc = "" +
	"\n" +
	"%master_internal/master_internal.proto\x121jony4.projects.jnqx.video.extract.master_internal\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\"\xa3\x02\n" +
	"\x05Media\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"media_name\x18\x01 \x01(\tR\tmediaName\x12P\n" +
	"\x04type\x18\x02 \x01(\x0e2<.jony4.projects.jnqx.video.extract.master_internal.MediaTypeR\x04type\x12\x1d\n" +
	"\n" +
	"source_url\x18\a \x01(\tR\tsourceUrl\x12$\n" +
	"\rtranscription\x18\x14 \x01(\tR\rtranscription\x12\x10\n" +
	"\x03lrc\x18\x15 \x01(\tR\x03lrc\x12\x10\n" +
	"\x03vtt\x18\x17 \x01(\tR\x03vtt\x12\x14\n" +
	"\x05graph\x18\x19 \x01(\tR\x05graph\x12\x1a\n" +
	"\bkeywords\x18\x1a \x01(\tR\bkeywords\"T\n" +
	"\x0ePresetQuestion\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\"\xaa\x01\n" +
	"\x12TaskQuestionAnswer\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x19\n" +
	"\bmedia_id\x18\x02 \x01(\tR\amediaId\x12,\n" +
	"\x12preset_question_id\x18\x03 \x01(\tR\x10presetQuestionId\x12\x1a\n" +
	"\bquestion\x18\x04 \x01(\tR\bquestion\x12\x16\n" +
	"\x06answer\x18\x05 \x01(\tR\x06answer\"\x7f\n" +
	"\x1cMediaQuestionAnswersResponse\x12_\n" +
	"\aanswers\x18\x01 \x03(\v2E.jony4.projects.jnqx.video.extract.master_internal.TaskQuestionAnswerR\aanswers\"w\n" +
	"\x06Device\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x16\n" +
	"\x06online\x18\x03 \x01(\bR\x06online\x12$\n" +
	"\x0elast_ping_time\x18\x04 \x01(\x03R\flastPingTime\"\xa1\x01\n" +
	"\x17TaskUpdateStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12U\n" +
	"\x06status\x18\x02 \x01(\x0e2=.jony4.projects.jnqx.video.extract.master_internal.TaskStatusR\x06status\x12\x1f\n" +
	"\vstatus_desc\x18\x03 \x01(\tR\n" +
	"statusDesc\"\x1a\n" +
	"\x18TaskUpdateStatusResponse\"1\n" +
	"\x12TaskReceiveRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\"b\n" +
	"\x13TaskReceiveResponse\x12K\n" +
	"\x04task\x18\x01 \x01(\v27.jony4.projects.jnqx.video.extract.master_internal.TaskR\x04task\"\xcb\x04\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1b\n" +
	"\ttask_name\x18\x01 \x01(\tR\btaskName\x12\x1b\n" +
	"\ttask_type\x18\x02 \x01(\tR\btaskType\x12\x1b\n" +
	"\tdevice_id\x18\x03 \x01(\tR\bdeviceId\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x19\n" +
	"\bmedia_id\x18\x05 \x01(\tR\amediaId\x12.\n" +
	"\x13preset_question_ids\x18\x06 \x01(\tR\x11presetQuestionIds\x12\x1d\n" +
	"\n" +
	"start_time\x18\n" +
	" \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\v \x01(\x03R\aendTime\x12\x1f\n" +
	"\vstatus_desc\x18\f \x01(\tR\n" +
	"statusDesc\x12\x1d\n" +
	"\n" +
	"model_name\x18\r \x01(\tR\tmodelName\x12w\n" +
	"\x10preset_questions\x18\x14 \x03(\v2L.jony4.projects.jnqx.video.extract.master_internal.Task.PresetQuestionsEntryR\x0fpresetQuestions\x1a\x85\x01\n" +
	"\x14PresetQuestionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12W\n" +
	"\x05value\x18\x02 \x01(\v2A.jony4.projects.jnqx.video.extract.master_internal.PresetQuestionR\x05value:\x028\x01\"\x12\n" +
	"\x10MediaListRequest\"e\n" +
	"\x11MediaListResponse\x12P\n" +
	"\x06medias\x18\x01 \x03(\v28.jony4.projects.jnqx.video.extract.master_internal.MediaR\x06medias*\xf0\x01\n" +
	"\tMediaType\x12\x12\n" +
	"\x0eMediaTypeVIDEO\x10\x00\x12\x12\n" +
	"\x0eMediaTypeAUDIO\x10\x01\x12\x12\n" +
	"\x0eMediaTypeIMAGE\x10\x02\x12\x11\n" +
	"\rMediaTypeTEXT\x10\x03\x12\x10\n" +
	"\fMediaTypePDF\x10\x04\x12\x10\n" +
	"\fMediaTypeDOC\x10\x05\x12\x10\n" +
	"\fMediaTypePPT\x10\x06\x12\x10\n" +
	"\fMediaTypeXLS\x10\a\x12\x11\n" +
	"\rMediaTypeHTML\x10\b\x12\x15\n" +
	"\x11MediaTypeMarkdown\x10\t\x12\x10\n" +
	"\fMediaTypeCSV\x10\n" +
	"\x12\x10\n" +
	"\fMediaTypeEml\x10\v*c\n" +
	"\n" +
	"TaskStatus\x12\x12\n" +
	"\x0eTaskStatusINIT\x10\x00\x12\x15\n" +
	"\x11TaskStatusRUNNING\x10\x01\x12\x15\n" +
	"\x11TaskStatusFINNISH\x10\x02\x12\x13\n" +
	"\x0fTaskStatusERROR\x10\x032\x89\a\n" +
	"\x15MasterInternalService\x12\xcb\x01\n" +
	"\x10TaskUpdateStatus\x12J.jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusRequest\x1aK.jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\"\x16/api/tasks/{id}/status\x12\xb8\x01\n" +
	"\vTaskReceive\x12E.jony4.projects.jnqx.video.extract.master_internal.TaskReceiveRequest\x1aF.jony4.projects.jnqx.video.extract.master_internal.TaskReceiveResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\"\x12/api/tasks/receive\x12\x9b\x01\n" +
	"\vMediaUpdate\x128.jony4.projects.jnqx.video.extract.master_internal.Media\x1a8.jony4.projects.jnqx.video.extract.master_internal.Media\"\x18\x82\xd3\xe4\x93\x02\x12\x1a\x10/api/medias/{id}\x12\x9c\x01\n" +
	"\x0eDeviceRegister\x129.jony4.projects.jnqx.video.extract.master_internal.Device\x1a9.jony4.projects.jnqx.video.extract.master_internal.Device\"\x14\x82\xd3\xe4\x93\x02\x0e\"\f/api/devices\x12\xa9\x01\n" +
	"\n" +
	"DevicePing\x129.jony4.projects.jnqx.video.extract.master_internal.Device\x1a9.jony4.projects.jnqx.video.extract.master_internal.Device\"%\x82\xd3\xe4\x93\x02\x1f\"\x1d/api/devices/{device_id}/pingB\x88\x02\x92A\xbd\x01\x12\x83\x01\n" +
	"\x0fMaster Internal\x12\x13Master Internal API\"\x15\n" +
	"\x05Jony4\x1a\<EMAIL>*=\n" +
	"\n" +
	"Apache 2.0\x12/http://www.apache.org/licenses/LICENSE-2.0.html2\x051.0.0\x1a\x0e127.0.0.1:8962*\x01\x012\x10application/json:\x10application/jsonZEgithub.com/OmniOrigin/projects-jnqx-video-extract/api/master_internalb\x06proto3"

var (
	file_master_internal_master_internal_proto_rawDescOnce sync.Once
	file_master_internal_master_internal_proto_rawDescData []byte
)

func file_master_internal_master_internal_proto_rawDescGZIP() []byte {
	file_master_internal_master_internal_proto_rawDescOnce.Do(func() {
		file_master_internal_master_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_master_internal_master_internal_proto_rawDesc), len(file_master_internal_master_internal_proto_rawDesc)))
	})
	return file_master_internal_master_internal_proto_rawDescData
}

var file_master_internal_master_internal_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_master_internal_master_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_master_internal_master_internal_proto_goTypes = []any{
	(MediaType)(0),                       // 0: jony4.projects.jnqx.video.extract.master_internal.MediaType
	(TaskStatus)(0),                      // 1: jony4.projects.jnqx.video.extract.master_internal.TaskStatus
	(*Media)(nil),                        // 2: jony4.projects.jnqx.video.extract.master_internal.Media
	(*PresetQuestion)(nil),               // 3: jony4.projects.jnqx.video.extract.master_internal.PresetQuestion
	(*TaskQuestionAnswer)(nil),           // 4: jony4.projects.jnqx.video.extract.master_internal.TaskQuestionAnswer
	(*MediaQuestionAnswersResponse)(nil), // 5: jony4.projects.jnqx.video.extract.master_internal.MediaQuestionAnswersResponse
	(*Device)(nil),                       // 6: jony4.projects.jnqx.video.extract.master_internal.Device
	(*TaskUpdateStatusRequest)(nil),      // 7: jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusRequest
	(*TaskUpdateStatusResponse)(nil),     // 8: jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusResponse
	(*TaskReceiveRequest)(nil),           // 9: jony4.projects.jnqx.video.extract.master_internal.TaskReceiveRequest
	(*TaskReceiveResponse)(nil),          // 10: jony4.projects.jnqx.video.extract.master_internal.TaskReceiveResponse
	(*Task)(nil),                         // 11: jony4.projects.jnqx.video.extract.master_internal.Task
	(*MediaListRequest)(nil),             // 12: jony4.projects.jnqx.video.extract.master_internal.MediaListRequest
	(*MediaListResponse)(nil),            // 13: jony4.projects.jnqx.video.extract.master_internal.MediaListResponse
	nil,                                  // 14: jony4.projects.jnqx.video.extract.master_internal.Task.PresetQuestionsEntry
}
var file_master_internal_master_internal_proto_depIdxs = []int32{
	0,  // 0: jony4.projects.jnqx.video.extract.master_internal.Media.type:type_name -> jony4.projects.jnqx.video.extract.master_internal.MediaType
	4,  // 1: jony4.projects.jnqx.video.extract.master_internal.MediaQuestionAnswersResponse.answers:type_name -> jony4.projects.jnqx.video.extract.master_internal.TaskQuestionAnswer
	1,  // 2: jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusRequest.status:type_name -> jony4.projects.jnqx.video.extract.master_internal.TaskStatus
	11, // 3: jony4.projects.jnqx.video.extract.master_internal.TaskReceiveResponse.task:type_name -> jony4.projects.jnqx.video.extract.master_internal.Task
	14, // 4: jony4.projects.jnqx.video.extract.master_internal.Task.preset_questions:type_name -> jony4.projects.jnqx.video.extract.master_internal.Task.PresetQuestionsEntry
	2,  // 5: jony4.projects.jnqx.video.extract.master_internal.MediaListResponse.medias:type_name -> jony4.projects.jnqx.video.extract.master_internal.Media
	3,  // 6: jony4.projects.jnqx.video.extract.master_internal.Task.PresetQuestionsEntry.value:type_name -> jony4.projects.jnqx.video.extract.master_internal.PresetQuestion
	7,  // 7: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.TaskUpdateStatus:input_type -> jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusRequest
	9,  // 8: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.TaskReceive:input_type -> jony4.projects.jnqx.video.extract.master_internal.TaskReceiveRequest
	2,  // 9: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.MediaUpdate:input_type -> jony4.projects.jnqx.video.extract.master_internal.Media
	6,  // 10: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.DeviceRegister:input_type -> jony4.projects.jnqx.video.extract.master_internal.Device
	6,  // 11: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.DevicePing:input_type -> jony4.projects.jnqx.video.extract.master_internal.Device
	8,  // 12: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.TaskUpdateStatus:output_type -> jony4.projects.jnqx.video.extract.master_internal.TaskUpdateStatusResponse
	10, // 13: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.TaskReceive:output_type -> jony4.projects.jnqx.video.extract.master_internal.TaskReceiveResponse
	2,  // 14: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.MediaUpdate:output_type -> jony4.projects.jnqx.video.extract.master_internal.Media
	6,  // 15: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.DeviceRegister:output_type -> jony4.projects.jnqx.video.extract.master_internal.Device
	6,  // 16: jony4.projects.jnqx.video.extract.master_internal.MasterInternalService.DevicePing:output_type -> jony4.projects.jnqx.video.extract.master_internal.Device
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_master_internal_master_internal_proto_init() }
func file_master_internal_master_internal_proto_init() {
	if File_master_internal_master_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_master_internal_master_internal_proto_rawDesc), len(file_master_internal_master_internal_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_master_internal_master_internal_proto_goTypes,
		DependencyIndexes: file_master_internal_master_internal_proto_depIdxs,
		EnumInfos:         file_master_internal_master_internal_proto_enumTypes,
		MessageInfos:      file_master_internal_master_internal_proto_msgTypes,
	}.Build()
	File_master_internal_master_internal_proto = out.File
	file_master_internal_master_internal_proto_goTypes = nil
	file_master_internal_master_internal_proto_depIdxs = nil
}
