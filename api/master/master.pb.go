// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: master/master.proto

package master

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MediaType int32

const (
	MediaType_MediaTypeVIDEO    MediaType = 0
	MediaType_MediaTypeAUDIO    MediaType = 1
	MediaType_MediaTypeIMAGE    MediaType = 2
	MediaType_MediaTypeTEXT     MediaType = 3
	MediaType_MediaTypePDF      MediaType = 4
	MediaType_MediaTypeDOC      MediaType = 5
	MediaType_MediaTypePPT      MediaType = 6
	MediaType_MediaTypeXLS      MediaType = 7
	MediaType_MediaTypeHTML     MediaType = 8
	MediaType_MediaTypeMarkdown MediaType = 9
	MediaType_MediaTypeCSV      MediaType = 10
	MediaType_MediaTypeEml      MediaType = 11
)

// Enum value maps for MediaType.
var (
	MediaType_name = map[int32]string{
		0:  "MediaTypeVIDEO",
		1:  "MediaTypeAUDIO",
		2:  "MediaTypeIMAGE",
		3:  "MediaTypeTEXT",
		4:  "MediaTypePDF",
		5:  "MediaTypeDOC",
		6:  "MediaTypePPT",
		7:  "MediaTypeXLS",
		8:  "MediaTypeHTML",
		9:  "MediaTypeMarkdown",
		10: "MediaTypeCSV",
		11: "MediaTypeEml",
	}
	MediaType_value = map[string]int32{
		"MediaTypeVIDEO":    0,
		"MediaTypeAUDIO":    1,
		"MediaTypeIMAGE":    2,
		"MediaTypeTEXT":     3,
		"MediaTypePDF":      4,
		"MediaTypeDOC":      5,
		"MediaTypePPT":      6,
		"MediaTypeXLS":      7,
		"MediaTypeHTML":     8,
		"MediaTypeMarkdown": 9,
		"MediaTypeCSV":      10,
		"MediaTypeEml":      11,
	}
)

func (x MediaType) Enum() *MediaType {
	p := new(MediaType)
	*p = x
	return p
}

func (x MediaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediaType) Descriptor() protoreflect.EnumDescriptor {
	return file_master_master_proto_enumTypes[0].Descriptor()
}

func (MediaType) Type() protoreflect.EnumType {
	return &file_master_master_proto_enumTypes[0]
}

func (x MediaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MediaType.Descriptor instead.
func (MediaType) EnumDescriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{0}
}

type TaskType int32

const (
	TaskType_TaskTypeVIDEO TaskType = 0
	TaskType_TaskTypeAUDIO TaskType = 1
	TaskType_TaskTypeTEXT  TaskType = 2
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0: "TaskTypeVIDEO",
		1: "TaskTypeAUDIO",
		2: "TaskTypeTEXT",
	}
	TaskType_value = map[string]int32{
		"TaskTypeVIDEO": 0,
		"TaskTypeAUDIO": 1,
		"TaskTypeTEXT":  2,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_master_master_proto_enumTypes[1].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_master_master_proto_enumTypes[1]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{1}
}

type TaskStatus int32

const (
	TaskStatus_TaskStatusINIT    TaskStatus = 0
	TaskStatus_TaskStatusRUNNING TaskStatus = 1
	TaskStatus_TaskStatusFINNISH TaskStatus = 2
	TaskStatus_TaskStatusERROR   TaskStatus = 3
)

// Enum value maps for TaskStatus.
var (
	TaskStatus_name = map[int32]string{
		0: "TaskStatusINIT",
		1: "TaskStatusRUNNING",
		2: "TaskStatusFINNISH",
		3: "TaskStatusERROR",
	}
	TaskStatus_value = map[string]int32{
		"TaskStatusINIT":    0,
		"TaskStatusRUNNING": 1,
		"TaskStatusFINNISH": 2,
		"TaskStatusERROR":   3,
	}
)

func (x TaskStatus) Enum() *TaskStatus {
	p := new(TaskStatus)
	*p = x
	return p
}

func (x TaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_master_master_proto_enumTypes[2].Descriptor()
}

func (TaskStatus) Type() protoreflect.EnumType {
	return &file_master_master_proto_enumTypes[2]
}

func (x TaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus.Descriptor instead.
func (TaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{2}
}

// RoleType defines the type of role.
// RoleType 定义了角色的类型。
type RoleType int32

const (
	RoleType_user      RoleType = 0
	RoleType_assistant RoleType = 1
	RoleType_system    RoleType = 2
)

// Enum value maps for RoleType.
var (
	RoleType_name = map[int32]string{
		0: "user",
		1: "assistant",
		2: "system",
	}
	RoleType_value = map[string]int32{
		"user":      0,
		"assistant": 1,
		"system":    2,
	}
)

func (x RoleType) Enum() *RoleType {
	p := new(RoleType)
	*p = x
	return p
}

func (x RoleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleType) Descriptor() protoreflect.EnumDescriptor {
	return file_master_master_proto_enumTypes[3].Descriptor()
}

func (RoleType) Type() protoreflect.EnumType {
	return &file_master_master_proto_enumTypes[3]
}

func (x RoleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleType.Descriptor instead.
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{3}
}

// finish_reason 停止生成的原因，枚举值.
// 默认非结束状态：none.
// 因结束符停止生成：stop.
// 因达到最大生成长度停止生成：length.
// 因触发敏感词停止生成： sensitive.
// 因触发模型上下文长度限制： context.
type FinishReason int32

const (
	FinishReason_none           FinishReason = 0
	FinishReason_stop           FinishReason = 1
	FinishReason_length         FinishReason = 2
	FinishReason_sensitive      FinishReason = 3
	FinishReason_context        FinishReason = 4
	FinishReason_function_call  FinishReason = 5
	FinishReason_tool_calls     FinishReason = 6
	FinishReason_content_filter FinishReason = 7
	FinishReason_null           FinishReason = 8
)

// Enum value maps for FinishReason.
var (
	FinishReason_name = map[int32]string{
		0: "none",
		1: "stop",
		2: "length",
		3: "sensitive",
		4: "context",
		5: "function_call",
		6: "tool_calls",
		7: "content_filter",
		8: "null",
	}
	FinishReason_value = map[string]int32{
		"none":           0,
		"stop":           1,
		"length":         2,
		"sensitive":      3,
		"context":        4,
		"function_call":  5,
		"tool_calls":     6,
		"content_filter": 7,
		"null":           8,
	}
)

func (x FinishReason) Enum() *FinishReason {
	p := new(FinishReason)
	*p = x
	return p
}

func (x FinishReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FinishReason) Descriptor() protoreflect.EnumDescriptor {
	return file_master_master_proto_enumTypes[4].Descriptor()
}

func (FinishReason) Type() protoreflect.EnumType {
	return &file_master_master_proto_enumTypes[4]
}

func (x FinishReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FinishReason.Descriptor instead.
func (FinishReason) EnumDescriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{4}
}

// MediaCreateRequest is the request structure for creating a media.
// MediaCreateRequest 是创建媒体的请求结构。
type MediaCreateRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	MediaName string                 `protobuf:"bytes,1,opt,name=media_name,json=mediaName,proto3" json:"media_name,omitempty"`
	// 媒体类型，默认为 MediaTypeVIDEO.
	Type          MediaType `protobuf:"varint,2,opt,name=type,proto3,enum=jony4.projects.jnqx.video.extract.master.MediaType" json:"type,omitempty"`
	SourceUrl     string    `protobuf:"bytes,3,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaCreateRequest) Reset() {
	*x = MediaCreateRequest{}
	mi := &file_master_master_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaCreateRequest) ProtoMessage() {}

func (x *MediaCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaCreateRequest.ProtoReflect.Descriptor instead.
func (*MediaCreateRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{0}
}

func (x *MediaCreateRequest) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *MediaCreateRequest) GetType() MediaType {
	if x != nil {
		return x.Type
	}
	return MediaType_MediaTypeVIDEO
}

func (x *MediaCreateRequest) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

// MediaCreateResponse is the response structure for creating a media.
// MediaCreateResponse 是创建媒体的响应结构。
type MediaCreateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	MediaName     string                 `protobuf:"bytes,2,opt,name=media_name,json=mediaName,proto3" json:"media_name,omitempty"`
	Type          MediaType              `protobuf:"varint,3,opt,name=type,proto3,enum=jony4.projects.jnqx.video.extract.master.MediaType" json:"type,omitempty"`
	SourceUrl     string                 `protobuf:"bytes,4,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaCreateResponse) Reset() {
	*x = MediaCreateResponse{}
	mi := &file_master_master_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaCreateResponse) ProtoMessage() {}

func (x *MediaCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaCreateResponse.ProtoReflect.Descriptor instead.
func (*MediaCreateResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{1}
}

func (x *MediaCreateResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MediaCreateResponse) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *MediaCreateResponse) GetType() MediaType {
	if x != nil {
		return x.Type
	}
	return MediaType_MediaTypeVIDEO
}

func (x *MediaCreateResponse) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

// Media defines the structure of a media.
// Media 定义了媒体的结构。
type Media struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	MediaName     string                 `protobuf:"bytes,1,opt,name=media_name,json=mediaName,proto3" json:"media_name,omitempty"`
	Type          MediaType              `protobuf:"varint,2,opt,name=type,proto3,enum=jony4.projects.jnqx.video.extract.master.MediaType" json:"type,omitempty"`
	SourceUrl     string                 `protobuf:"bytes,7,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	Lrc           string                 `protobuf:"bytes,21,opt,name=lrc,proto3" json:"lrc,omitempty"`
	Transcription string                 `protobuf:"bytes,22,opt,name=transcription,proto3" json:"transcription,omitempty"`
	Vtt           string                 `protobuf:"bytes,23,opt,name=vtt,proto3" json:"vtt,omitempty"`
	Transcript    []*Transcript          `protobuf:"bytes,24,rep,name=transcript,proto3" json:"transcript,omitempty"`
	Graph         string                 `protobuf:"bytes,25,opt,name=graph,proto3" json:"graph,omitempty"`
	Keywords      string                 `protobuf:"bytes,26,opt,name=keywords,proto3" json:"keywords,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Media) Reset() {
	*x = Media{}
	mi := &file_master_master_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Media) ProtoMessage() {}

func (x *Media) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Media.ProtoReflect.Descriptor instead.
func (*Media) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{2}
}

func (x *Media) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Media) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *Media) GetType() MediaType {
	if x != nil {
		return x.Type
	}
	return MediaType_MediaTypeVIDEO
}

func (x *Media) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *Media) GetLrc() string {
	if x != nil {
		return x.Lrc
	}
	return ""
}

func (x *Media) GetTranscription() string {
	if x != nil {
		return x.Transcription
	}
	return ""
}

func (x *Media) GetVtt() string {
	if x != nil {
		return x.Vtt
	}
	return ""
}

func (x *Media) GetTranscript() []*Transcript {
	if x != nil {
		return x.Transcript
	}
	return nil
}

func (x *Media) GetGraph() string {
	if x != nil {
		return x.Graph
	}
	return ""
}

func (x *Media) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

type Transcript struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	From          int32                  `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`
	To            int32                  `protobuf:"varint,3,opt,name=to,proto3" json:"to,omitempty"`
	Speaker       string                 `protobuf:"bytes,4,opt,name=speaker,proto3" json:"speaker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transcript) Reset() {
	*x = Transcript{}
	mi := &file_master_master_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transcript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transcript) ProtoMessage() {}

func (x *Transcript) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transcript.ProtoReflect.Descriptor instead.
func (*Transcript) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{3}
}

func (x *Transcript) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Transcript) GetFrom() int32 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *Transcript) GetTo() int32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *Transcript) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

// Task defines the structure of a task.
// Task 定义了任务的结构。
type Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	// 【必填】任务名称
	TaskName string `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	// 【必填】任务类型，默认为 TaskTypeVIDEO.
	TaskType TaskType `protobuf:"varint,2,opt,name=task_type,json=taskType,proto3,enum=jony4.projects.jnqx.video.extract.master.TaskType" json:"task_type,omitempty"`
	// 【选填】设备 id
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 【选填】任务状态，默认为 TaskStatusINIT.
	Status TaskStatus `protobuf:"varint,4,opt,name=status,proto3,enum=jony4.projects.jnqx.video.extract.master.TaskStatus" json:"status,omitempty"`
	// 【必填】媒体 id
	MediaId string `protobuf:"bytes,5,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	// 【选填】预设问题 id
	PresetQuestionIds []string `protobuf:"bytes,6,rep,name=preset_question_ids,json=presetQuestionIds,proto3" json:"preset_question_ids,omitempty"`
	// 【选填】开始时间
	StartTime int64 `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 【选填】结束时间
	EndTime int64 `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 【必填】回调地址
	CallbackUrl string `protobuf:"bytes,12,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	// 【选填】任务状态描述
	StatusDesc string `protobuf:"bytes,13,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	// 【选填】使用的模型名称，默认走本地模型
	ModelName     string `protobuf:"bytes,14,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_master_master_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{4}
}

func (x *Task) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Task) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *Task) GetTaskType() TaskType {
	if x != nil {
		return x.TaskType
	}
	return TaskType_TaskTypeVIDEO
}

func (x *Task) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Task) GetStatus() TaskStatus {
	if x != nil {
		return x.Status
	}
	return TaskStatus_TaskStatusINIT
}

func (x *Task) GetMediaId() string {
	if x != nil {
		return x.MediaId
	}
	return ""
}

func (x *Task) GetPresetQuestionIds() []string {
	if x != nil {
		return x.PresetQuestionIds
	}
	return nil
}

func (x *Task) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Task) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Task) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *Task) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *Task) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// PresetQuestion defines the structure of a preset question.
// PresetQuestion 定义了预设问题的结构。
type PresetQuestion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,100,opt,name=id,proto3" json:"id,omitempty"`
	// 【必填】问题
	Question string `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	// 【选填】程序自动生成问题的 prompt 内容，无需传入
	Prompt        string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetQuestion) Reset() {
	*x = PresetQuestion{}
	mi := &file_master_master_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetQuestion) ProtoMessage() {}

func (x *PresetQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetQuestion.ProtoReflect.Descriptor instead.
func (*PresetQuestion) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{5}
}

func (x *PresetQuestion) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PresetQuestion) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *PresetQuestion) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

// PresetQuestionDeleteResponse is the response structure for deleting a preset question.
// PresetQuestionDeleteResponse 是删除预设问题的响应结构。
type PresetQuestionDeleteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetQuestionDeleteResponse) Reset() {
	*x = PresetQuestionDeleteResponse{}
	mi := &file_master_master_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetQuestionDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetQuestionDeleteResponse) ProtoMessage() {}

func (x *PresetQuestionDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetQuestionDeleteResponse.ProtoReflect.Descriptor instead.
func (*PresetQuestionDeleteResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{6}
}

// PresetQuestionListRequest is the request structure for listing preset questions.
// PresetQuestionListRequest 是列出预设问题的请求结构。
type PresetQuestionListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetQuestionListRequest) Reset() {
	*x = PresetQuestionListRequest{}
	mi := &file_master_master_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetQuestionListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetQuestionListRequest) ProtoMessage() {}

func (x *PresetQuestionListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetQuestionListRequest.ProtoReflect.Descriptor instead.
func (*PresetQuestionListRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{7}
}

// PresetQuestionListResponse is the response structure for listing preset questions.
// PresetQuestionListResponse 是列出预设问题的响应结构。
type PresetQuestionListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Questions     []*PresetQuestion      `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetQuestionListResponse) Reset() {
	*x = PresetQuestionListResponse{}
	mi := &file_master_master_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetQuestionListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetQuestionListResponse) ProtoMessage() {}

func (x *PresetQuestionListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetQuestionListResponse.ProtoReflect.Descriptor instead.
func (*PresetQuestionListResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{8}
}

func (x *PresetQuestionListResponse) GetQuestions() []*PresetQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

// MediaQuestionAnswer defines the structure of a media question answer.
// MediaQuestionAnswer 定义了媒体问题答案的结构。
type QuestionAnswer struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PresetQuestionId string                 `protobuf:"bytes,1,opt,name=preset_question_id,json=presetQuestionId,proto3" json:"preset_question_id,omitempty"`
	Question         string                 `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Answer           string                 `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *QuestionAnswer) Reset() {
	*x = QuestionAnswer{}
	mi := &file_master_master_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestionAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionAnswer) ProtoMessage() {}

func (x *QuestionAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionAnswer.ProtoReflect.Descriptor instead.
func (*QuestionAnswer) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{9}
}

func (x *QuestionAnswer) GetPresetQuestionId() string {
	if x != nil {
		return x.PresetQuestionId
	}
	return ""
}

func (x *QuestionAnswer) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *QuestionAnswer) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// TaskPresetQuestionAnswersRequest is the request structure for getting all preset question answers.
// TaskPresetQuestionAnswersRequest 是获取所有预设问题答案的请求结构。
type TaskPresetQuestionAnswersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskPresetQuestionAnswersRequest) Reset() {
	*x = TaskPresetQuestionAnswersRequest{}
	mi := &file_master_master_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskPresetQuestionAnswersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskPresetQuestionAnswersRequest) ProtoMessage() {}

func (x *TaskPresetQuestionAnswersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskPresetQuestionAnswersRequest.ProtoReflect.Descriptor instead.
func (*TaskPresetQuestionAnswersRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{10}
}

func (x *TaskPresetQuestionAnswersRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

// TaskPresetQuestionAnswersResponse is the response structure for getting all preset question answers.
// TaskPresetQuestionAnswersResponse 是获取所有预设问题答案的响应结构。
type TaskPresetQuestionAnswersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Answers       []*QuestionAnswer      `protobuf:"bytes,1,rep,name=answers,proto3" json:"answers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskPresetQuestionAnswersResponse) Reset() {
	*x = TaskPresetQuestionAnswersResponse{}
	mi := &file_master_master_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskPresetQuestionAnswersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskPresetQuestionAnswersResponse) ProtoMessage() {}

func (x *TaskPresetQuestionAnswersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskPresetQuestionAnswersResponse.ProtoReflect.Descriptor instead.
func (*TaskPresetQuestionAnswersResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{11}
}

func (x *TaskPresetQuestionAnswersResponse) GetAnswers() []*QuestionAnswer {
	if x != nil {
		return x.Answers
	}
	return nil
}

// TaskListRequest is the request structure for listing tasks.
// TaskListRequest 是列出任务的请求结构。
type TaskListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskListRequest) Reset() {
	*x = TaskListRequest{}
	mi := &file_master_master_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListRequest) ProtoMessage() {}

func (x *TaskListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListRequest.ProtoReflect.Descriptor instead.
func (*TaskListRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{12}
}

// TaskListResponse is the response structure for listing tasks.
// TaskListResponse 是列出任务的响应结构。
type TaskListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tasks         []*Task                `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskListResponse) Reset() {
	*x = TaskListResponse{}
	mi := &file_master_master_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListResponse) ProtoMessage() {}

func (x *TaskListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListResponse.ProtoReflect.Descriptor instead.
func (*TaskListResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{13}
}

func (x *TaskListResponse) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// TaskGetRequest is the request structure for getting a task.
// TaskGetRequest 是获取任务的请求结构。
type TaskGetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskGetRequest) Reset() {
	*x = TaskGetRequest{}
	mi := &file_master_master_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskGetRequest) ProtoMessage() {}

func (x *TaskGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskGetRequest.ProtoReflect.Descriptor instead.
func (*TaskGetRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{14}
}

func (x *TaskGetRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// TaskDeleteRequest is the request structure for deleting a task.
// TaskDeleteRequest 是删除任务的请求结构。
type TaskDeleteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskDeleteRequest) Reset() {
	*x = TaskDeleteRequest{}
	mi := &file_master_master_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDeleteRequest) ProtoMessage() {}

func (x *TaskDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDeleteRequest.ProtoReflect.Descriptor instead.
func (*TaskDeleteRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{15}
}

func (x *TaskDeleteRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// TaskDeleteResponse is the response structure for deleting a task.
// TaskDeleteResponse 是删除任务的响应结构。
type TaskDeleteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskDeleteResponse) Reset() {
	*x = TaskDeleteResponse{}
	mi := &file_master_master_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDeleteResponse) ProtoMessage() {}

func (x *TaskDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDeleteResponse.ProtoReflect.Descriptor instead.
func (*TaskDeleteResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{16}
}

// TaskRedoResponse is the response structure for redoing a task.
// TaskRedoResponse 是重做任务的响应结构。
type TaskRedoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskRedoResponse) Reset() {
	*x = TaskRedoResponse{}
	mi := &file_master_master_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskRedoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRedoResponse) ProtoMessage() {}

func (x *TaskRedoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRedoResponse.ProtoReflect.Descriptor instead.
func (*TaskRedoResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{17}
}

// ChatCompletionsRequest is the request structure for generating chat completions.
// ChatCompletionsRequest 是生成聊天完成的请求结构。
type ChatCompletionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 输入给模型的对话上下文，数组中的每个对象为聊天的上下文信息。请注意，数组中最后一项必须为 user.
	Messages []*MessageWithRole `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	// 是否使用流式传输, 默认false. 如果开启，数据将按照data-only SSE（server-sent events）返回中间结果，并以 data: [stop] 结束.
	Stream bool `protobuf:"varint,2,opt,name=stream,proto3" json:"stream,omitempty"`
	// 【选填】使用的模型名称，默认走本地模型
	ModelName string `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// 任务 ID
	TaskId        string `protobuf:"bytes,10,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatCompletionsRequest) Reset() {
	*x = ChatCompletionsRequest{}
	mi := &file_master_master_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsRequest) ProtoMessage() {}

func (x *ChatCompletionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsRequest.ProtoReflect.Descriptor instead.
func (*ChatCompletionsRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{18}
}

func (x *ChatCompletionsRequest) GetMessages() []*MessageWithRole {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *ChatCompletionsRequest) GetStream() bool {
	if x != nil {
		return x.Stream
	}
	return false
}

func (x *ChatCompletionsRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ChatCompletionsRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

// ChatCompletionsRequest is the request structure for generating chat completions.
// ChatCompletionsRequest 是生成聊天完成的请求结构。
type ChatCompletionsNormalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 输入给模型的对话上下文，数组中的每个对象为聊天的上下文信息。请注意，数组中最后一项必须为 user.
	Messages []*MessageWithRole `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	// 是否使用流式传输, 默认false. 如果开启，数据将按照data-only SSE（server-sent events）返回中间结果，并以 data: [stop] 结束.
	Stream bool `protobuf:"varint,2,opt,name=stream,proto3" json:"stream,omitempty"`
	// 【选填】使用的模型名称，默认走本地模型
	ModelName     string `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatCompletionsNormalRequest) Reset() {
	*x = ChatCompletionsNormalRequest{}
	mi := &file_master_master_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsNormalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsNormalRequest) ProtoMessage() {}

func (x *ChatCompletionsNormalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsNormalRequest.ProtoReflect.Descriptor instead.
func (*ChatCompletionsNormalRequest) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{19}
}

func (x *ChatCompletionsNormalRequest) GetMessages() []*MessageWithRole {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *ChatCompletionsNormalRequest) GetStream() bool {
	if x != nil {
		return x.Stream
	}
	return false
}

func (x *ChatCompletionsNormalRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// MessageWithRole defines the structure of a message with role.
// MessageWithRole 定义了带角色的消息的结构。
type MessageWithRole struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户输入.
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 消息作者的角色，枚举值.
	// system
	// user
	// assistant
	Role          RoleType `protobuf:"varint,2,opt,name=role,proto3,enum=jony4.projects.jnqx.video.extract.master.RoleType" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageWithRole) Reset() {
	*x = MessageWithRole{}
	mi := &file_master_master_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageWithRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageWithRole) ProtoMessage() {}

func (x *MessageWithRole) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageWithRole.ProtoReflect.Descriptor instead.
func (*MessageWithRole) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{20}
}

func (x *MessageWithRole) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MessageWithRole) GetRole() RoleType {
	if x != nil {
		return x.Role
	}
	return RoleType_user
}

// ChatCompletionsResponse is the response structure for generating chat completions.
// ChatCompletionsResponse 是生成聊天完成的响应结构。
type ChatCompletionsResponse struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	Data          *ChatCompletionsResponse_CompletionsData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Status        *Status                                  `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatCompletionsResponse) Reset() {
	*x = ChatCompletionsResponse{}
	mi := &file_master_master_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsResponse) ProtoMessage() {}

func (x *ChatCompletionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsResponse.ProtoReflect.Descriptor instead.
func (*ChatCompletionsResponse) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{21}
}

func (x *ChatCompletionsResponse) GetData() *ChatCompletionsResponse_CompletionsData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ChatCompletionsResponse) GetStatus() *Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type Status struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Status) Reset() {
	*x = Status{}
	mi := &file_master_master_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status) ProtoMessage() {}

func (x *Status) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status.ProtoReflect.Descriptor instead.
func (*Status) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{22}
}

func (x *Status) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Status) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ChatCompletionsResponse_Choices struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 非流式请求时，生成的回复内容.
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// 流式请求时，生成的回复内容.
	Delta string `protobuf:"bytes,2,opt,name=delta,proto3" json:"delta,omitempty"`
	// finish_reason 停止生成的原因，枚举值.
	FinishReason  FinishReason `protobuf:"varint,3,opt,name=finish_reason,json=finishReason,proto3,enum=jony4.projects.jnqx.video.extract.master.FinishReason" json:"finish_reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatCompletionsResponse_Choices) Reset() {
	*x = ChatCompletionsResponse_Choices{}
	mi := &file_master_master_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsResponse_Choices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsResponse_Choices) ProtoMessage() {}

func (x *ChatCompletionsResponse_Choices) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsResponse_Choices.ProtoReflect.Descriptor instead.
func (*ChatCompletionsResponse_Choices) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{21, 0}
}

func (x *ChatCompletionsResponse_Choices) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatCompletionsResponse_Choices) GetDelta() string {
	if x != nil {
		return x.Delta
	}
	return ""
}

func (x *ChatCompletionsResponse_Choices) GetFinishReason() FinishReason {
	if x != nil {
		return x.FinishReason
	}
	return FinishReason_none
}

type ChatCompletionsResponse_Usage struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PromptTokens     uint32                 `protobuf:"varint,1,opt,name=prompt_tokens,json=promptTokens,proto3" json:"prompt_tokens,omitempty"`
	CompletionTokens uint32                 `protobuf:"varint,2,opt,name=completion_tokens,json=completionTokens,proto3" json:"completion_tokens,omitempty"`
	TotalTokens      uint32                 `protobuf:"varint,3,opt,name=total_tokens,json=totalTokens,proto3" json:"total_tokens,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ChatCompletionsResponse_Usage) Reset() {
	*x = ChatCompletionsResponse_Usage{}
	mi := &file_master_master_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsResponse_Usage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsResponse_Usage) ProtoMessage() {}

func (x *ChatCompletionsResponse_Usage) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsResponse_Usage.ProtoReflect.Descriptor instead.
func (*ChatCompletionsResponse_Usage) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{21, 1}
}

func (x *ChatCompletionsResponse_Usage) GetPromptTokens() uint32 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *ChatCompletionsResponse_Usage) GetCompletionTokens() uint32 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

func (x *ChatCompletionsResponse_Usage) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

type ChatCompletionsResponse_CompletionsData struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Id            string                             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Choices       []*ChatCompletionsResponse_Choices `protobuf:"bytes,2,rep,name=choices,proto3" json:"choices,omitempty"`
	Usage         *ChatCompletionsResponse_Usage     `protobuf:"bytes,3,opt,name=usage,proto3" json:"usage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatCompletionsResponse_CompletionsData) Reset() {
	*x = ChatCompletionsResponse_CompletionsData{}
	mi := &file_master_master_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCompletionsResponse_CompletionsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCompletionsResponse_CompletionsData) ProtoMessage() {}

func (x *ChatCompletionsResponse_CompletionsData) ProtoReflect() protoreflect.Message {
	mi := &file_master_master_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCompletionsResponse_CompletionsData.ProtoReflect.Descriptor instead.
func (*ChatCompletionsResponse_CompletionsData) Descriptor() ([]byte, []int) {
	return file_master_master_proto_rawDescGZIP(), []int{21, 2}
}

func (x *ChatCompletionsResponse_CompletionsData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ChatCompletionsResponse_CompletionsData) GetChoices() []*ChatCompletionsResponse_Choices {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *ChatCompletionsResponse_CompletionsData) GetUsage() *ChatCompletionsResponse_Usage {
	if x != nil {
		return x.Usage
	}
	return nil
}

var File_master_master_proto protoreflect.FileDescriptor

const file_master_master_proto_rawDesc = "" +
	"\n" +
	"\x13master/master.proto\x12(jony4.projects.jnqx.video.extract.master\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\"\x9b\x01\n" +
	"\x12MediaCreateRequest\x12\x1d\n" +
	"\n" +
	"media_name\x18\x01 \x01(\tR\tmediaName\x12G\n" +
	"\x04type\x18\x02 \x01(\x0e23.jony4.projects.jnqx.video.extract.master.MediaTypeR\x04type\x12\x1d\n" +
	"\n" +
	"source_url\x18\x03 \x01(\tR\tsourceUrl\"\xac\x01\n" +
	"\x13MediaCreateResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"media_name\x18\x02 \x01(\tR\tmediaName\x12G\n" +
	"\x04type\x18\x03 \x01(\x0e23.jony4.projects.jnqx.video.extract.master.MediaTypeR\x04type\x12\x1d\n" +
	"\n" +
	"source_url\x18\x04 \x01(\tR\tsourceUrl\"\xf0\x02\n" +
	"\x05Media\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"media_name\x18\x01 \x01(\tR\tmediaName\x12G\n" +
	"\x04type\x18\x02 \x01(\x0e23.jony4.projects.jnqx.video.extract.master.MediaTypeR\x04type\x12\x1d\n" +
	"\n" +
	"source_url\x18\a \x01(\tR\tsourceUrl\x12\x10\n" +
	"\x03lrc\x18\x15 \x01(\tR\x03lrc\x12$\n" +
	"\rtranscription\x18\x16 \x01(\tR\rtranscription\x12\x10\n" +
	"\x03vtt\x18\x17 \x01(\tR\x03vtt\x12T\n" +
	"\n" +
	"transcript\x18\x18 \x03(\v24.jony4.projects.jnqx.video.extract.master.TranscriptR\n" +
	"transcript\x12\x14\n" +
	"\x05graph\x18\x19 \x01(\tR\x05graph\x12\x1a\n" +
	"\bkeywords\x18\x1a \x01(\tR\bkeywords\"d\n" +
	"\n" +
	"Transcript\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x12\n" +
	"\x04from\x18\x02 \x01(\x05R\x04from\x12\x0e\n" +
	"\x02to\x18\x03 \x01(\x05R\x02to\x12\x18\n" +
	"\aspeaker\x18\x04 \x01(\tR\aspeaker\"\xd7\x03\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1b\n" +
	"\ttask_name\x18\x01 \x01(\tR\btaskName\x12O\n" +
	"\ttask_type\x18\x02 \x01(\x0e22.jony4.projects.jnqx.video.extract.master.TaskTypeR\btaskType\x12\x1b\n" +
	"\tdevice_id\x18\x03 \x01(\tR\bdeviceId\x12L\n" +
	"\x06status\x18\x04 \x01(\x0e24.jony4.projects.jnqx.video.extract.master.TaskStatusR\x06status\x12\x19\n" +
	"\bmedia_id\x18\x05 \x01(\tR\amediaId\x12.\n" +
	"\x13preset_question_ids\x18\x06 \x03(\tR\x11presetQuestionIds\x12\x1d\n" +
	"\n" +
	"start_time\x18\n" +
	" \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\v \x01(\x03R\aendTime\x12!\n" +
	"\fcallback_url\x18\f \x01(\tR\vcallbackUrl\x12\x1f\n" +
	"\vstatus_desc\x18\r \x01(\tR\n" +
	"statusDesc\x12\x1d\n" +
	"\n" +
	"model_name\x18\x0e \x01(\tR\tmodelName\"T\n" +
	"\x0ePresetQuestion\x12\x0e\n" +
	"\x02id\x18d \x01(\tR\x02id\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\"\x1e\n" +
	"\x1cPresetQuestionDeleteResponse\"\x1b\n" +
	"\x19PresetQuestionListRequest\"t\n" +
	"\x1aPresetQuestionListResponse\x12V\n" +
	"\tquestions\x18\x01 \x03(\v28.jony4.projects.jnqx.video.extract.master.PresetQuestionR\tquestions\"r\n" +
	"\x0eQuestionAnswer\x12,\n" +
	"\x12preset_question_id\x18\x01 \x01(\tR\x10presetQuestionId\x12\x1a\n" +
	"\bquestion\x18\x02 \x01(\tR\bquestion\x12\x16\n" +
	"\x06answer\x18\x03 \x01(\tR\x06answer\";\n" +
	" TaskPresetQuestionAnswersRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\"w\n" +
	"!TaskPresetQuestionAnswersResponse\x12R\n" +
	"\aanswers\x18\x01 \x03(\v28.jony4.projects.jnqx.video.extract.master.QuestionAnswerR\aanswers\"\x11\n" +
	"\x0fTaskListRequest\"X\n" +
	"\x10TaskListResponse\x12D\n" +
	"\x05tasks\x18\x01 \x03(\v2..jony4.projects.jnqx.video.extract.master.TaskR\x05tasks\" \n" +
	"\x0eTaskGetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"#\n" +
	"\x11TaskDeleteRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x14\n" +
	"\x12TaskDeleteResponse\"\x12\n" +
	"\x10TaskRedoResponse\"\xbf\x01\n" +
	"\x16ChatCompletionsRequest\x12U\n" +
	"\bmessages\x18\x01 \x03(\v29.jony4.projects.jnqx.video.extract.master.MessageWithRoleR\bmessages\x12\x16\n" +
	"\x06stream\x18\x02 \x01(\bR\x06stream\x12\x1d\n" +
	"\n" +
	"model_name\x18\x03 \x01(\tR\tmodelName\x12\x17\n" +
	"\atask_id\x18\n" +
	" \x01(\tR\x06taskId\"\xac\x01\n" +
	"\x1cChatCompletionsNormalRequest\x12U\n" +
	"\bmessages\x18\x01 \x03(\v29.jony4.projects.jnqx.video.extract.master.MessageWithRoleR\bmessages\x12\x16\n" +
	"\x06stream\x18\x02 \x01(\bR\x06stream\x12\x1d\n" +
	"\n" +
	"model_name\x18\x03 \x01(\tR\tmodelName\"s\n" +
	"\x0fMessageWithRole\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12F\n" +
	"\x04role\x18\x02 \x01(\x0e22.jony4.projects.jnqx.video.extract.master.RoleTypeR\x04role\"\xc9\x05\n" +
	"\x17ChatCompletionsResponse\x12e\n" +
	"\x04data\x18\x01 \x01(\v2Q.jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.CompletionsDataR\x04data\x12H\n" +
	"\x06status\x18\x02 \x01(\v20.jony4.projects.jnqx.video.extract.master.StatusR\x06status\x1a\x96\x01\n" +
	"\aChoices\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x14\n" +
	"\x05delta\x18\x02 \x01(\tR\x05delta\x12[\n" +
	"\rfinish_reason\x18\x03 \x01(\x0e26.jony4.projects.jnqx.video.extract.master.FinishReasonR\ffinishReason\x1a|\n" +
	"\x05Usage\x12#\n" +
	"\rprompt_tokens\x18\x01 \x01(\rR\fpromptTokens\x12+\n" +
	"\x11completion_tokens\x18\x02 \x01(\rR\x10completionTokens\x12!\n" +
	"\ftotal_tokens\x18\x03 \x01(\rR\vtotalTokens\x1a\xe5\x01\n" +
	"\x0fCompletionsData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12c\n" +
	"\achoices\x18\x02 \x03(\v2I.jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.ChoicesR\achoices\x12]\n" +
	"\x05usage\x18\x03 \x01(\v2G.jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.UsageR\x05usage\"6\n" +
	"\x06Status\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*\xf0\x01\n" +
	"\tMediaType\x12\x12\n" +
	"\x0eMediaTypeVIDEO\x10\x00\x12\x12\n" +
	"\x0eMediaTypeAUDIO\x10\x01\x12\x12\n" +
	"\x0eMediaTypeIMAGE\x10\x02\x12\x11\n" +
	"\rMediaTypeTEXT\x10\x03\x12\x10\n" +
	"\fMediaTypePDF\x10\x04\x12\x10\n" +
	"\fMediaTypeDOC\x10\x05\x12\x10\n" +
	"\fMediaTypePPT\x10\x06\x12\x10\n" +
	"\fMediaTypeXLS\x10\a\x12\x11\n" +
	"\rMediaTypeHTML\x10\b\x12\x15\n" +
	"\x11MediaTypeMarkdown\x10\t\x12\x10\n" +
	"\fMediaTypeCSV\x10\n" +
	"\x12\x10\n" +
	"\fMediaTypeEml\x10\v*B\n" +
	"\bTaskType\x12\x11\n" +
	"\rTaskTypeVIDEO\x10\x00\x12\x11\n" +
	"\rTaskTypeAUDIO\x10\x01\x12\x10\n" +
	"\fTaskTypeTEXT\x10\x02*c\n" +
	"\n" +
	"TaskStatus\x12\x12\n" +
	"\x0eTaskStatusINIT\x10\x00\x12\x15\n" +
	"\x11TaskStatusRUNNING\x10\x01\x12\x15\n" +
	"\x11TaskStatusFINNISH\x10\x02\x12\x13\n" +
	"\x0fTaskStatusERROR\x10\x03*/\n" +
	"\bRoleType\x12\b\n" +
	"\x04user\x10\x00\x12\r\n" +
	"\tassistant\x10\x01\x12\n" +
	"\n" +
	"\x06system\x10\x02*\x8b\x01\n" +
	"\fFinishReason\x12\b\n" +
	"\x04none\x10\x00\x12\b\n" +
	"\x04stop\x10\x01\x12\n" +
	"\n" +
	"\x06length\x10\x02\x12\r\n" +
	"\tsensitive\x10\x03\x12\v\n" +
	"\acontext\x10\x04\x12\x11\n" +
	"\rfunction_call\x10\x05\x12\x0e\n" +
	"\n" +
	"tool_calls\x10\x06\x12\x12\n" +
	"\x0econtent_filter\x10\a\x12\b\n" +
	"\x04null\x10\b2\xf5\t\n" +
	"\rMasterService\x12\xa2\x01\n" +
	"\vMediaCreate\x12<.jony4.projects.jnqx.video.extract.master.MediaCreateRequest\x1a=.jony4.projects.jnqx.video.extract.master.MediaCreateResponse\"\x16\x82\xd3\xe4\x93\x02\x10:\x01*\"\v/api/medias\x12\x86\x01\n" +
	"\bMediaGet\x12/.jony4.projects.jnqx.video.extract.master.Media\x1a/.jony4.projects.jnqx.video.extract.master.Media\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/medias/{id}\x12\x83\x01\n" +
	"\n" +
	"TaskCreate\x12..jony4.projects.jnqx.video.extract.master.Task\x1a..jony4.projects.jnqx.video.extract.master.Task\"\x15\x82\xd3\xe4\x93\x02\x0f:\x01*\"\n" +
	"/api/tasks\x12\x8c\x01\n" +
	"\aTaskGet\x128.jony4.projects.jnqx.video.extract.master.TaskGetRequest\x1a..jony4.projects.jnqx.video.extract.master.Task\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/api/tasks/{id}\x12\xa0\x01\n" +
	"\n" +
	"TaskDelete\x12;.jony4.projects.jnqx.video.extract.master.TaskDeleteRequest\x1a<.jony4.projects.jnqx.video.extract.master.TaskDeleteResponse\"\x17\x82\xd3\xe4\x93\x02\x11*\x0f/api/tasks/{id}\x12\x95\x01\n" +
	"\bTaskList\x129.jony4.projects.jnqx.video.extract.master.TaskListRequest\x1a:.jony4.projects.jnqx.video.extract.master.TaskListResponse\"\x12\x82\xd3\xe4\x93\x02\f\x12\n" +
	"/api/tasks\x12\x97\x01\n" +
	"\bTaskRedo\x12..jony4.projects.jnqx.video.extract.master.Task\x1a:.jony4.projects.jnqx.video.extract.master.TaskRedoResponse\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/api/tasks/{id}/redo\x12\xcb\x01\n" +
	"\x19TaskChatNormalCompletions\x12F.jony4.projects.jnqx.video.extract.master.ChatCompletionsNormalRequest\x1aA.jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/api/v1/chat/completionsB\xfa\x01\x92A\xb8\x01\x12\x7f\n" +
	"\rMaster Public\x12\x11Master Public API\"\x15\n" +
	"\x05Jony4\x1a\<EMAIL>*=\n" +
	"\n" +
	"Apache 2.0\x12/http://www.apache.org/licenses/LICENSE-2.0.html2\x051.0.0\x1a\x0e127.0.0.1:8962*\x01\x012\x10application/json:\x10application/jsonZ<github.com/OmniOrigin/projects-jnqx-video-extract/api/masterb\x06proto3"

var (
	file_master_master_proto_rawDescOnce sync.Once
	file_master_master_proto_rawDescData []byte
)

func file_master_master_proto_rawDescGZIP() []byte {
	file_master_master_proto_rawDescOnce.Do(func() {
		file_master_master_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_master_master_proto_rawDesc), len(file_master_master_proto_rawDesc)))
	})
	return file_master_master_proto_rawDescData
}

var file_master_master_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_master_master_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_master_master_proto_goTypes = []any{
	(MediaType)(0),                                  // 0: jony4.projects.jnqx.video.extract.master.MediaType
	(TaskType)(0),                                   // 1: jony4.projects.jnqx.video.extract.master.TaskType
	(TaskStatus)(0),                                 // 2: jony4.projects.jnqx.video.extract.master.TaskStatus
	(RoleType)(0),                                   // 3: jony4.projects.jnqx.video.extract.master.RoleType
	(FinishReason)(0),                               // 4: jony4.projects.jnqx.video.extract.master.FinishReason
	(*MediaCreateRequest)(nil),                      // 5: jony4.projects.jnqx.video.extract.master.MediaCreateRequest
	(*MediaCreateResponse)(nil),                     // 6: jony4.projects.jnqx.video.extract.master.MediaCreateResponse
	(*Media)(nil),                                   // 7: jony4.projects.jnqx.video.extract.master.Media
	(*Transcript)(nil),                              // 8: jony4.projects.jnqx.video.extract.master.Transcript
	(*Task)(nil),                                    // 9: jony4.projects.jnqx.video.extract.master.Task
	(*PresetQuestion)(nil),                          // 10: jony4.projects.jnqx.video.extract.master.PresetQuestion
	(*PresetQuestionDeleteResponse)(nil),            // 11: jony4.projects.jnqx.video.extract.master.PresetQuestionDeleteResponse
	(*PresetQuestionListRequest)(nil),               // 12: jony4.projects.jnqx.video.extract.master.PresetQuestionListRequest
	(*PresetQuestionListResponse)(nil),              // 13: jony4.projects.jnqx.video.extract.master.PresetQuestionListResponse
	(*QuestionAnswer)(nil),                          // 14: jony4.projects.jnqx.video.extract.master.QuestionAnswer
	(*TaskPresetQuestionAnswersRequest)(nil),        // 15: jony4.projects.jnqx.video.extract.master.TaskPresetQuestionAnswersRequest
	(*TaskPresetQuestionAnswersResponse)(nil),       // 16: jony4.projects.jnqx.video.extract.master.TaskPresetQuestionAnswersResponse
	(*TaskListRequest)(nil),                         // 17: jony4.projects.jnqx.video.extract.master.TaskListRequest
	(*TaskListResponse)(nil),                        // 18: jony4.projects.jnqx.video.extract.master.TaskListResponse
	(*TaskGetRequest)(nil),                          // 19: jony4.projects.jnqx.video.extract.master.TaskGetRequest
	(*TaskDeleteRequest)(nil),                       // 20: jony4.projects.jnqx.video.extract.master.TaskDeleteRequest
	(*TaskDeleteResponse)(nil),                      // 21: jony4.projects.jnqx.video.extract.master.TaskDeleteResponse
	(*TaskRedoResponse)(nil),                        // 22: jony4.projects.jnqx.video.extract.master.TaskRedoResponse
	(*ChatCompletionsRequest)(nil),                  // 23: jony4.projects.jnqx.video.extract.master.ChatCompletionsRequest
	(*ChatCompletionsNormalRequest)(nil),            // 24: jony4.projects.jnqx.video.extract.master.ChatCompletionsNormalRequest
	(*MessageWithRole)(nil),                         // 25: jony4.projects.jnqx.video.extract.master.MessageWithRole
	(*ChatCompletionsResponse)(nil),                 // 26: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse
	(*Status)(nil),                                  // 27: jony4.projects.jnqx.video.extract.master.Status
	(*ChatCompletionsResponse_Choices)(nil),         // 28: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.Choices
	(*ChatCompletionsResponse_Usage)(nil),           // 29: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.Usage
	(*ChatCompletionsResponse_CompletionsData)(nil), // 30: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.CompletionsData
}
var file_master_master_proto_depIdxs = []int32{
	0,  // 0: jony4.projects.jnqx.video.extract.master.MediaCreateRequest.type:type_name -> jony4.projects.jnqx.video.extract.master.MediaType
	0,  // 1: jony4.projects.jnqx.video.extract.master.MediaCreateResponse.type:type_name -> jony4.projects.jnqx.video.extract.master.MediaType
	0,  // 2: jony4.projects.jnqx.video.extract.master.Media.type:type_name -> jony4.projects.jnqx.video.extract.master.MediaType
	8,  // 3: jony4.projects.jnqx.video.extract.master.Media.transcript:type_name -> jony4.projects.jnqx.video.extract.master.Transcript
	1,  // 4: jony4.projects.jnqx.video.extract.master.Task.task_type:type_name -> jony4.projects.jnqx.video.extract.master.TaskType
	2,  // 5: jony4.projects.jnqx.video.extract.master.Task.status:type_name -> jony4.projects.jnqx.video.extract.master.TaskStatus
	10, // 6: jony4.projects.jnqx.video.extract.master.PresetQuestionListResponse.questions:type_name -> jony4.projects.jnqx.video.extract.master.PresetQuestion
	14, // 7: jony4.projects.jnqx.video.extract.master.TaskPresetQuestionAnswersResponse.answers:type_name -> jony4.projects.jnqx.video.extract.master.QuestionAnswer
	9,  // 8: jony4.projects.jnqx.video.extract.master.TaskListResponse.tasks:type_name -> jony4.projects.jnqx.video.extract.master.Task
	25, // 9: jony4.projects.jnqx.video.extract.master.ChatCompletionsRequest.messages:type_name -> jony4.projects.jnqx.video.extract.master.MessageWithRole
	25, // 10: jony4.projects.jnqx.video.extract.master.ChatCompletionsNormalRequest.messages:type_name -> jony4.projects.jnqx.video.extract.master.MessageWithRole
	3,  // 11: jony4.projects.jnqx.video.extract.master.MessageWithRole.role:type_name -> jony4.projects.jnqx.video.extract.master.RoleType
	30, // 12: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.data:type_name -> jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.CompletionsData
	27, // 13: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.status:type_name -> jony4.projects.jnqx.video.extract.master.Status
	4,  // 14: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.Choices.finish_reason:type_name -> jony4.projects.jnqx.video.extract.master.FinishReason
	28, // 15: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.CompletionsData.choices:type_name -> jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.Choices
	29, // 16: jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.CompletionsData.usage:type_name -> jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse.Usage
	5,  // 17: jony4.projects.jnqx.video.extract.master.MasterService.MediaCreate:input_type -> jony4.projects.jnqx.video.extract.master.MediaCreateRequest
	7,  // 18: jony4.projects.jnqx.video.extract.master.MasterService.MediaGet:input_type -> jony4.projects.jnqx.video.extract.master.Media
	9,  // 19: jony4.projects.jnqx.video.extract.master.MasterService.TaskCreate:input_type -> jony4.projects.jnqx.video.extract.master.Task
	19, // 20: jony4.projects.jnqx.video.extract.master.MasterService.TaskGet:input_type -> jony4.projects.jnqx.video.extract.master.TaskGetRequest
	20, // 21: jony4.projects.jnqx.video.extract.master.MasterService.TaskDelete:input_type -> jony4.projects.jnqx.video.extract.master.TaskDeleteRequest
	17, // 22: jony4.projects.jnqx.video.extract.master.MasterService.TaskList:input_type -> jony4.projects.jnqx.video.extract.master.TaskListRequest
	9,  // 23: jony4.projects.jnqx.video.extract.master.MasterService.TaskRedo:input_type -> jony4.projects.jnqx.video.extract.master.Task
	24, // 24: jony4.projects.jnqx.video.extract.master.MasterService.TaskChatNormalCompletions:input_type -> jony4.projects.jnqx.video.extract.master.ChatCompletionsNormalRequest
	6,  // 25: jony4.projects.jnqx.video.extract.master.MasterService.MediaCreate:output_type -> jony4.projects.jnqx.video.extract.master.MediaCreateResponse
	7,  // 26: jony4.projects.jnqx.video.extract.master.MasterService.MediaGet:output_type -> jony4.projects.jnqx.video.extract.master.Media
	9,  // 27: jony4.projects.jnqx.video.extract.master.MasterService.TaskCreate:output_type -> jony4.projects.jnqx.video.extract.master.Task
	9,  // 28: jony4.projects.jnqx.video.extract.master.MasterService.TaskGet:output_type -> jony4.projects.jnqx.video.extract.master.Task
	21, // 29: jony4.projects.jnqx.video.extract.master.MasterService.TaskDelete:output_type -> jony4.projects.jnqx.video.extract.master.TaskDeleteResponse
	18, // 30: jony4.projects.jnqx.video.extract.master.MasterService.TaskList:output_type -> jony4.projects.jnqx.video.extract.master.TaskListResponse
	22, // 31: jony4.projects.jnqx.video.extract.master.MasterService.TaskRedo:output_type -> jony4.projects.jnqx.video.extract.master.TaskRedoResponse
	26, // 32: jony4.projects.jnqx.video.extract.master.MasterService.TaskChatNormalCompletions:output_type -> jony4.projects.jnqx.video.extract.master.ChatCompletionsResponse
	25, // [25:33] is the sub-list for method output_type
	17, // [17:25] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_master_master_proto_init() }
func file_master_master_proto_init() {
	if File_master_master_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_master_master_proto_rawDesc), len(file_master_master_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_master_master_proto_goTypes,
		DependencyIndexes: file_master_master_proto_depIdxs,
		EnumInfos:         file_master_master_proto_enumTypes,
		MessageInfos:      file_master_master_proto_msgTypes,
	}.Build()
	File_master_master_proto = out.File
	file_master_master_proto_goTypes = nil
	file_master_master_proto_depIdxs = nil
}
