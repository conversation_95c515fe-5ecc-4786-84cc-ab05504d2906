{"swagger": "2.0", "info": {"title": "Master Public", "description": "Master Public API", "version": "1.0.0", "contact": {"name": "Jony4", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "127.0.0.1:8962", "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/medias": {"post": {"summary": "MediaCreate creates a new media.\nMediaCreate 创建一个新的媒体。", "operationId": "MasterService_MediaCreate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterMediaCreateResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/masterMediaCreateRequest"}}], "tags": ["MasterService"]}}, "/api/medias/{id}": {"get": {"summary": "MediaGet gets a media by its ID.\nMediaGet 通过其 ID 获取一个媒体。", "operationId": "MasterService_MediaGet", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterMedia"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "media_name", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "string", "enum": ["MediaTypeVIDEO", "MediaTypeAUDIO", "MediaTypeIMAGE", "MediaTypeTEXT", "MediaTypePDF", "MediaTypeDOC", "MediaTypePPT", "MediaTypeXLS", "MediaTypeHTML", "MediaTypeMarkdown", "MediaTypeCSV", "MediaTypeEml"], "default": "MediaTypeVIDEO"}, {"name": "source_url", "in": "query", "required": false, "type": "string"}, {"name": "lrc", "in": "query", "required": false, "type": "string"}, {"name": "transcription", "in": "query", "required": false, "type": "string"}, {"name": "vtt", "in": "query", "required": false, "type": "string"}, {"name": "graph", "in": "query", "required": false, "type": "string"}, {"name": "keywords", "in": "query", "required": false, "type": "string"}], "tags": ["MasterService"]}}, "/api/tasks": {"get": {"summary": "TaskList lists all tasks.\nTaskList 列出所有任务。", "operationId": "MasterService_TaskList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterTaskListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "tags": ["MasterService"]}, "post": {"summary": "TaskCreate creates a new task.\nTaskCreate 创建一个新的任务。", "operationId": "MasterService_TaskCreate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/masterTask"}}], "tags": ["MasterService"]}}, "/api/tasks/{id}": {"get": {"summary": "TaskGet gets a task by its ID.\nTaskGet 通过其 ID 获取一个任务。", "operationId": "MasterService_TaskGet", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["MasterService"]}, "delete": {"summary": "TaskDelete deletes a task.\nTaskDelete 删除一个任务。", "operationId": "MasterService_TaskDelete", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterTaskDeleteResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["MasterService"]}}, "/api/tasks/{id}/redo": {"post": {"summary": "TaskRedo redoes a task.\nTaskRedo 重做一个任务。", "operationId": "MasterService_TaskRedo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterTaskRedoResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/masterTask"}}], "tags": ["MasterService"]}}, "/api/v1/chat/completions": {"post": {"summary": "TaskChatCompletions generates chat completions.\nTaskChatCompletions 生成聊天完成。", "operationId": "MasterService_TaskChatNormalCompletions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/masterChatCompletionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/masterChatCompletionsNormalRequest"}}], "tags": ["MasterService"]}}}, "definitions": {"ChatCompletionsResponseChoices": {"type": "object", "properties": {"message": {"type": "string", "description": "非流式请求时，生成的回复内容."}, "delta": {"type": "string", "description": "流式请求时，生成的回复内容."}, "finish_reason": {"$ref": "#/definitions/masterFinishReason", "description": "finish_reason 停止生成的原因，枚举值."}}}, "ChatCompletionsResponseCompletionsData": {"type": "object", "properties": {"id": {"type": "string"}, "choices": {"type": "array", "items": {"$ref": "#/definitions/ChatCompletionsResponseChoices"}}, "usage": {"$ref": "#/definitions/ChatCompletionsResponseUsage"}}}, "ChatCompletionsResponseUsage": {"type": "object", "properties": {"prompt_tokens": {"type": "integer", "format": "int64"}, "completion_tokens": {"type": "integer", "format": "int64"}, "total_tokens": {"type": "integer", "format": "int64"}}}, "masterChatCompletionsNormalRequest": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/definitions/masterMessageWithRole"}, "description": "输入给模型的对话上下文，数组中的每个对象为聊天的上下文信息。请注意，数组中最后一项必须为 user."}, "stream": {"type": "boolean", "description": "是否使用流式传输, 默认false. 如果开启，数据将按照data-only SSE（server-sent events）返回中间结果，并以 data: [stop] 结束."}, "model_name": {"type": "string", "title": "【选填】使用的模型名称，默认走本地模型"}}, "title": "ChatCompletionsRequest is the request structure for generating chat completions.\nChatCompletionsRequest 是生成聊天完成的请求结构。"}, "masterChatCompletionsResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ChatCompletionsResponseCompletionsData"}, "status": {"$ref": "#/definitions/masterStatus"}}, "title": "ChatCompletionsResponse is the response structure for generating chat completions.\nChatCompletionsResponse 是生成聊天完成的响应结构。"}, "masterFinishReason": {"type": "string", "enum": ["none", "stop", "length", "sensitive", "context", "function_call", "tool_calls", "content_filter", "null"], "default": "none", "description": "finish_reason 停止生成的原因，枚举值.\n默认非结束状态：none.\n因结束符停止生成：stop.\n因达到最大生成长度停止生成：length.\n因触发敏感词停止生成： sensitive.\n因触发模型上下文长度限制： context."}, "masterMedia": {"type": "object", "properties": {"id": {"type": "string"}, "media_name": {"type": "string"}, "type": {"$ref": "#/definitions/masterMediaType"}, "source_url": {"type": "string"}, "lrc": {"type": "string"}, "transcription": {"type": "string"}, "vtt": {"type": "string"}, "transcript": {"type": "array", "items": {"$ref": "#/definitions/masterTranscript"}}, "graph": {"type": "string"}, "keywords": {"type": "string"}}, "title": "Media defines the structure of a media.\nMedia 定义了媒体的结构。"}, "masterMediaCreateRequest": {"type": "object", "properties": {"media_name": {"type": "string"}, "type": {"$ref": "#/definitions/masterMediaType", "description": "媒体类型，默认为 MediaTypeVIDEO."}, "source_url": {"type": "string"}}, "title": "MediaCreateRequest is the request structure for creating a media.\nMediaCreateRequest 是创建媒体的请求结构。"}, "masterMediaCreateResponse": {"type": "object", "properties": {"id": {"type": "string"}, "media_name": {"type": "string"}, "type": {"$ref": "#/definitions/masterMediaType"}, "source_url": {"type": "string"}}, "title": "MediaCreateResponse is the response structure for creating a media.\nMediaCreateResponse 是创建媒体的响应结构。"}, "masterMediaType": {"type": "string", "enum": ["MediaTypeVIDEO", "MediaTypeAUDIO", "MediaTypeIMAGE", "MediaTypeTEXT", "MediaTypePDF", "MediaTypeDOC", "MediaTypePPT", "MediaTypeXLS", "MediaTypeHTML", "MediaTypeMarkdown", "MediaTypeCSV", "MediaTypeEml"], "default": "MediaTypeVIDEO"}, "masterMessageWithRole": {"type": "object", "properties": {"content": {"type": "string", "description": "用户输入."}, "role": {"$ref": "#/definitions/masterRoleType", "title": "消息作者的角色，枚举值.\nsystem\nuser\nassistant"}}, "title": "MessageWithRole defines the structure of a message with role.\nMessageWithRole 定义了带角色的消息的结构。"}, "masterRoleType": {"type": "string", "enum": ["user", "assistant", "system"], "default": "user", "title": "RoleType defines the type of role.\nRoleType 定义了角色的类型。"}, "masterStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "masterTask": {"type": "object", "properties": {"id": {"type": "string"}, "task_name": {"type": "string", "title": "【必填】任务名称"}, "task_type": {"$ref": "#/definitions/masterTaskType", "description": "【必填】任务类型，默认为 TaskTypeVIDEO."}, "device_id": {"type": "string", "title": "【选填】设备 id"}, "status": {"$ref": "#/definitions/masterTaskStatus", "description": "【选填】任务状态，默认为 TaskStatusINIT."}, "media_id": {"type": "string", "title": "【必填】媒体 id"}, "preset_question_ids": {"type": "array", "items": {"type": "string"}, "title": "【选填】预设问题 id"}, "start_time": {"type": "string", "format": "int64", "title": "【选填】开始时间"}, "end_time": {"type": "string", "format": "int64", "title": "【选填】结束时间"}, "callback_url": {"type": "string", "title": "【必填】回调地址"}, "status_desc": {"type": "string", "title": "【选填】任务状态描述"}, "model_name": {"type": "string", "title": "【选填】使用的模型名称，默认走本地模型"}}, "title": "Task defines the structure of a task.\nTask 定义了任务的结构。"}, "masterTaskDeleteResponse": {"type": "object", "title": "TaskDeleteResponse is the response structure for deleting a task.\nTaskDeleteResponse 是删除任务的响应结构。"}, "masterTaskListResponse": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/definitions/masterTask"}}}, "title": "TaskListResponse is the response structure for listing tasks.\nTaskListResponse 是列出任务的响应结构。"}, "masterTaskRedoResponse": {"type": "object", "title": "TaskRedoResponse is the response structure for redoing a task.\nTaskRedoResponse 是重做任务的响应结构。"}, "masterTaskStatus": {"type": "string", "enum": ["TaskStatusINIT", "TaskStatusRUNNING", "TaskStatusFINNISH", "TaskStatusERROR"], "default": "TaskStatusINIT"}, "masterTaskType": {"type": "string", "enum": ["TaskTypeVIDEO", "TaskTypeAUDIO", "TaskTypeTEXT"], "default": "TaskTypeVIDEO"}, "masterTranscript": {"type": "object", "properties": {"content": {"type": "string"}, "from": {"type": "integer", "format": "int32"}, "to": {"type": "integer", "format": "int32"}, "speaker": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"type_url": {"type": "string"}, "value": {"type": "string", "format": "byte"}}}, "runtimeError": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}