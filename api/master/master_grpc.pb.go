// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: master/master.proto

package master

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MasterService_MediaCreate_FullMethodName               = "/jony4.projects.jnqx.video.extract.master.MasterService/MediaCreate"
	MasterService_MediaGet_FullMethodName                  = "/jony4.projects.jnqx.video.extract.master.MasterService/MediaGet"
	MasterService_TaskCreate_FullMethodName                = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskCreate"
	MasterService_TaskGet_FullMethodName                   = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskGet"
	MasterService_TaskDelete_FullMethodName                = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskDelete"
	MasterService_TaskList_FullMethodName                  = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskList"
	MasterService_TaskRedo_FullMethodName                  = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskRedo"
	MasterService_TaskChatNormalCompletions_FullMethodName = "/jony4.projects.jnqx.video.extract.master.MasterService/TaskChatNormalCompletions"
)

// MasterServiceClient is the client API for MasterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The MasterService service definition.
// 这个服务处理任务，媒体，预设问题和媒体问题答案。
type MasterServiceClient interface {
	// MediaCreate creates a new media.
	// MediaCreate 创建一个新的媒体。
	MediaCreate(ctx context.Context, in *MediaCreateRequest, opts ...grpc.CallOption) (*MediaCreateResponse, error)
	// MediaGet gets a media by its ID.
	// MediaGet 通过其 ID 获取一个媒体。
	MediaGet(ctx context.Context, in *Media, opts ...grpc.CallOption) (*Media, error)
	// TaskCreate creates a new task.
	// TaskCreate 创建一个新的任务。
	TaskCreate(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error)
	// TaskGet gets a task by its ID.
	// TaskGet 通过其 ID 获取一个任务。
	TaskGet(ctx context.Context, in *TaskGetRequest, opts ...grpc.CallOption) (*Task, error)
	// TaskDelete deletes a task.
	// TaskDelete 删除一个任务。
	TaskDelete(ctx context.Context, in *TaskDeleteRequest, opts ...grpc.CallOption) (*TaskDeleteResponse, error)
	// TaskList lists all tasks.
	// TaskList 列出所有任务。
	TaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error)
	// TaskRedo redoes a task.
	// TaskRedo 重做一个任务。
	TaskRedo(ctx context.Context, in *Task, opts ...grpc.CallOption) (*TaskRedoResponse, error)
	// TaskChatCompletions generates chat completions.
	// TaskChatCompletions 生成聊天完成。
	TaskChatNormalCompletions(ctx context.Context, in *ChatCompletionsNormalRequest, opts ...grpc.CallOption) (*ChatCompletionsResponse, error)
}

type masterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMasterServiceClient(cc grpc.ClientConnInterface) MasterServiceClient {
	return &masterServiceClient{cc}
}

func (c *masterServiceClient) MediaCreate(ctx context.Context, in *MediaCreateRequest, opts ...grpc.CallOption) (*MediaCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MediaCreateResponse)
	err := c.cc.Invoke(ctx, MasterService_MediaCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) MediaGet(ctx context.Context, in *Media, opts ...grpc.CallOption) (*Media, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Media)
	err := c.cc.Invoke(ctx, MasterService_MediaGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskCreate(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Task)
	err := c.cc.Invoke(ctx, MasterService_TaskCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskGet(ctx context.Context, in *TaskGetRequest, opts ...grpc.CallOption) (*Task, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Task)
	err := c.cc.Invoke(ctx, MasterService_TaskGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskDelete(ctx context.Context, in *TaskDeleteRequest, opts ...grpc.CallOption) (*TaskDeleteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskDeleteResponse)
	err := c.cc.Invoke(ctx, MasterService_TaskDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskListResponse)
	err := c.cc.Invoke(ctx, MasterService_TaskList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskRedo(ctx context.Context, in *Task, opts ...grpc.CallOption) (*TaskRedoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskRedoResponse)
	err := c.cc.Invoke(ctx, MasterService_TaskRedo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) TaskChatNormalCompletions(ctx context.Context, in *ChatCompletionsNormalRequest, opts ...grpc.CallOption) (*ChatCompletionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChatCompletionsResponse)
	err := c.cc.Invoke(ctx, MasterService_TaskChatNormalCompletions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MasterServiceServer is the server API for MasterService service.
// All implementations should embed UnimplementedMasterServiceServer
// for forward compatibility.
//
// The MasterService service definition.
// 这个服务处理任务，媒体，预设问题和媒体问题答案。
type MasterServiceServer interface {
	// MediaCreate creates a new media.
	// MediaCreate 创建一个新的媒体。
	MediaCreate(context.Context, *MediaCreateRequest) (*MediaCreateResponse, error)
	// MediaGet gets a media by its ID.
	// MediaGet 通过其 ID 获取一个媒体。
	MediaGet(context.Context, *Media) (*Media, error)
	// TaskCreate creates a new task.
	// TaskCreate 创建一个新的任务。
	TaskCreate(context.Context, *Task) (*Task, error)
	// TaskGet gets a task by its ID.
	// TaskGet 通过其 ID 获取一个任务。
	TaskGet(context.Context, *TaskGetRequest) (*Task, error)
	// TaskDelete deletes a task.
	// TaskDelete 删除一个任务。
	TaskDelete(context.Context, *TaskDeleteRequest) (*TaskDeleteResponse, error)
	// TaskList lists all tasks.
	// TaskList 列出所有任务。
	TaskList(context.Context, *TaskListRequest) (*TaskListResponse, error)
	// TaskRedo redoes a task.
	// TaskRedo 重做一个任务。
	TaskRedo(context.Context, *Task) (*TaskRedoResponse, error)
	// TaskChatCompletions generates chat completions.
	// TaskChatCompletions 生成聊天完成。
	TaskChatNormalCompletions(context.Context, *ChatCompletionsNormalRequest) (*ChatCompletionsResponse, error)
}

// UnimplementedMasterServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMasterServiceServer struct{}

func (UnimplementedMasterServiceServer) MediaCreate(context.Context, *MediaCreateRequest) (*MediaCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MediaCreate not implemented")
}
func (UnimplementedMasterServiceServer) MediaGet(context.Context, *Media) (*Media, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MediaGet not implemented")
}
func (UnimplementedMasterServiceServer) TaskCreate(context.Context, *Task) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskCreate not implemented")
}
func (UnimplementedMasterServiceServer) TaskGet(context.Context, *TaskGetRequest) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGet not implemented")
}
func (UnimplementedMasterServiceServer) TaskDelete(context.Context, *TaskDeleteRequest) (*TaskDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskDelete not implemented")
}
func (UnimplementedMasterServiceServer) TaskList(context.Context, *TaskListRequest) (*TaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskList not implemented")
}
func (UnimplementedMasterServiceServer) TaskRedo(context.Context, *Task) (*TaskRedoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskRedo not implemented")
}
func (UnimplementedMasterServiceServer) TaskChatNormalCompletions(context.Context, *ChatCompletionsNormalRequest) (*ChatCompletionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskChatNormalCompletions not implemented")
}
func (UnimplementedMasterServiceServer) testEmbeddedByValue() {}

// UnsafeMasterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MasterServiceServer will
// result in compilation errors.
type UnsafeMasterServiceServer interface {
	mustEmbedUnimplementedMasterServiceServer()
}

func RegisterMasterServiceServer(s grpc.ServiceRegistrar, srv MasterServiceServer) {
	// If the following call pancis, it indicates UnimplementedMasterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MasterService_ServiceDesc, srv)
}

func _MasterService_MediaCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MediaCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).MediaCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_MediaCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).MediaCreate(ctx, req.(*MediaCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_MediaGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Media)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).MediaGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_MediaGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).MediaGet(ctx, req.(*Media))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskCreate(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskGet(ctx, req.(*TaskGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskDelete(ctx, req.(*TaskDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskList(ctx, req.(*TaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskRedo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskRedo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskRedo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskRedo(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_TaskChatNormalCompletions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatCompletionsNormalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).TaskChatNormalCompletions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MasterService_TaskChatNormalCompletions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).TaskChatNormalCompletions(ctx, req.(*ChatCompletionsNormalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MasterService_ServiceDesc is the grpc.ServiceDesc for MasterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MasterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "jony4.projects.jnqx.video.extract.master.MasterService",
	HandlerType: (*MasterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MediaCreate",
			Handler:    _MasterService_MediaCreate_Handler,
		},
		{
			MethodName: "MediaGet",
			Handler:    _MasterService_MediaGet_Handler,
		},
		{
			MethodName: "TaskCreate",
			Handler:    _MasterService_TaskCreate_Handler,
		},
		{
			MethodName: "TaskGet",
			Handler:    _MasterService_TaskGet_Handler,
		},
		{
			MethodName: "TaskDelete",
			Handler:    _MasterService_TaskDelete_Handler,
		},
		{
			MethodName: "TaskList",
			Handler:    _MasterService_TaskList_Handler,
		},
		{
			MethodName: "TaskRedo",
			Handler:    _MasterService_TaskRedo_Handler,
		},
		{
			MethodName: "TaskChatNormalCompletions",
			Handler:    _MasterService_TaskChatNormalCompletions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "master/master.proto",
}
