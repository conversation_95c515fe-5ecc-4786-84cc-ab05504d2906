# v1.6.3 (2024-06-28)

* No change notes available for this release.

# v1.6.2 (2024-03-29)

* No change notes available for this release.

# v1.6.1 (2024-02-21)

* No change notes available for this release.

# v1.6.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.

# v1.5.4 (2023-12-07)

* No change notes available for this release.

# v1.5.3 (2023-11-30)

* No change notes available for this release.

# v1.5.2 (2023-11-29)

* No change notes available for this release.

# v1.5.1 (2023-11-15)

* No change notes available for this release.

# v1.5.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).

# v1.4.14 (2023-10-06)

* No change notes available for this release.

# v1.4.13 (2023-08-18)

* No change notes available for this release.

# v1.4.12 (2023-08-07)

* No change notes available for this release.

# v1.4.11 (2023-07-31)

* No change notes available for this release.

# v1.4.10 (2022-12-02)

* No change notes available for this release.

# v1.4.9 (2022-10-24)

* No change notes available for this release.

# v1.4.8 (2022-09-14)

* No change notes available for this release.

# v1.4.7 (2022-09-02)

* No change notes available for this release.

# v1.4.6 (2022-08-31)

* No change notes available for this release.

# v1.4.5 (2022-08-29)

* No change notes available for this release.

# v1.4.4 (2022-08-09)

* No change notes available for this release.

# v1.4.3 (2022-06-29)

* No change notes available for this release.

# v1.4.2 (2022-06-07)

* No change notes available for this release.

# v1.4.1 (2022-03-24)

* No change notes available for this release.

# v1.4.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.3.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.2.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.1.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.0.0 (2021-11-06)

* **Announcement**: Support has been added for AWS EventStream APIs for Kinesis, S3, and Transcribe Streaming. Support for the Lex Runtime V2 EventStream API will be added in a future release.
* **Release**: Protocol support has been added for AWS event stream.
* **Feature**: Updated `github.com/aws/smithy-go` to latest version

