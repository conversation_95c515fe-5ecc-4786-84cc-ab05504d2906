# v1.17.23 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.22 (2024-06-26)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.21 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.20 (2024-06-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.19 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.18 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.17 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.16 (2024-05-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.15 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.14 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.13 (2024-05-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.12 (2024-05-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.11 (2024-04-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.10 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.9 (2024-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.8 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.7 (2024-03-07)

* **Bug Fix**: Remove dependency on go-cmp.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.6 (2024-03-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.5 (2024-03-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.4 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.3 (2024-02-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.2 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.1 (2024-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.16 (2024-01-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.15 (2024-01-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.14 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.13 (2023-12-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.12 (2023-12-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.11 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.10 (2023-12-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.9 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.8 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.7 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.6 (2023-11-28.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.5 (2023-11-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.4 (2023-11-21)

* **Bug Fix**: Don't expect error responses to have a JSON payload in the endpointcreds provider.

# v1.16.3 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.2 (2023-11-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.1 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.0 (2023-11-14)

* **Feature**: Add support for dynamic auth token from file and EKS container host in absolute/relative URIs in the HTTP credential provider.

# v1.15.2 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.1 (2023-11-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.0 (2023-11-01)

* **Feature**: Adds support for configured endpoints via environment variables and the AWS shared configuration file.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.43 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.42 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.41 (2023-10-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.40 (2023-09-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.39 (2023-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.38 (2023-09-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.37 (2023-09-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.36 (2023-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.35 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.34 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.33 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.32 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.31 (2023-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.30 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.29 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.28 (2023-07-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.27 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.26 (2023-06-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.25 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.24 (2023-05-09)

* No change notes available for this release.

# v1.13.23 (2023-05-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.22 (2023-05-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.21 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.20 (2023-04-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.19 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.18 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.17 (2023-03-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.16 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.15 (2023-02-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.14 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.13 (2023-02-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.12 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.11 (2023-02-01)

* No change notes available for this release.

# v1.13.10 (2023-01-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.9 (2023-01-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.8 (2023-01-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.7 (2022-12-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.6 (2022-12-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.5 (2022-12-15)

* **Bug Fix**: Unify logic between shared config and in finding home directory
* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.4 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.3 (2022-11-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.2 (2022-11-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.1 (2022-11-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.0 (2022-11-11)

* **Announcement**: When using the SSOTokenProvider, a previous implementation incorrectly compensated for invalid SSOTokenProvider configurations in the shared profile. This has been fixed via PR #1903 and tracked in issue #1846
* **Feature**: Adds token refresh support (via SSOTokenProvider) when using the SSOCredentialProvider

# v1.12.24 (2022-11-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.23 (2022-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.22 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.21 (2022-09-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.20 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.19 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.18 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.17 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.16 (2022-08-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.15 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.14 (2022-08-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.13 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.12 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.11 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.10 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.9 (2022-07-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.8 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.7 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.6 (2022-06-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.5 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.4 (2022-05-26)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.3 (2022-05-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.2 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.1 (2022-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.0 (2022-04-25)

* **Feature**: Adds Duration and Policy options that can be used when creating stscreds.WebIdentityRoleProvider credentials provider.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.2 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.1 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.0 (2022-03-23)

* **Feature**: Update `ec2rolecreds` package's `Provider` to implememnt support for CredentialsCache new optional caching strategy interfaces, HandleFailRefreshCredentialsCacheStrategy and AdjustExpiresByCredentialsCacheStrategy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.0 (2022-02-24)

* **Feature**: Adds support for `SourceIdentity` to `stscreds.AssumeRoleProvider` [#1588](https://github.com/aws/aws-sdk-go-v2/pull/1588). Fixes [#1575](https://github.com/aws/aws-sdk-go-v2/issues/1575)
* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.5 (2021-12-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.4 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.3 (2021-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.2 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.1 (2021-11-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.0 (2021-11-06)

* **Feature**: The SDK now supports configuration of FIPS and DualStack endpoints using environment variables, shared configuration, or programmatically.
* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.0 (2021-10-21)

* **Feature**: Updated  to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.3 (2021-10-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.2 (2021-09-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.1 (2021-09-10)

* **Documentation**: Fixes the AssumeRoleProvider's documentation for using custom TokenProviders.

# v1.4.0 (2021-08-27)

* **Feature**: Adds support for Tags and TransitiveTagKeys to stscreds.AssumeRoleProvider. Closes https://github.com/aws/aws-sdk-go-v2/issues/723
* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.3 (2021-08-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.2 (2021-08-04)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.1 (2021-07-15)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.0 (2021-06-25)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Bug Fix**: Fixed example usages of aws.CredentialsCache ([#1275](https://github.com/aws/aws-sdk-go-v2/pull/1275))
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.1 (2021-05-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.0 (2021-05-14)

* **Feature**: Constant has been added to modules to enable runtime version inspection for reporting.
* **Dependency Update**: Updated to the latest SDK module versions

