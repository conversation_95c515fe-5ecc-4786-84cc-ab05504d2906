# v1.17.4 (2024-07-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.3 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.2 (2024-06-26)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.1 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.17.0 (2024-06-18)

* **Feature**: Track usage of various AWS SDK features in user-agent string.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.25 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.24 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.23 (2024-06-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.22 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.21 (2024-05-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.20 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.19 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.18 (2024-05-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.17 (2024-05-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.16 (2024-05-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.15 (2024-04-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.14 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.13 (2024-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.12 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.11 (2024-03-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.10 (2024-03-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.9 (2024-03-07)

* **Bug Fix**: Remove dependency on go-cmp.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.8 (2024-03-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.7 (2024-03-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.6 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.5 (2024-02-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.4 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.3 (2024-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.2 (2024-02-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.1 (2024-02-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.15 (2024-01-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.14 (2024-01-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.13 (2024-01-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.12 (2024-01-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.11 (2024-01-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.10 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.9 (2023-12-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.8 (2023-12-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.7 (2023-12-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.6 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.5 (2023-12-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.4 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.3 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.2 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.1 (2023-11-28.3)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.0 (2023-11-28.2)

* **Feature**: Add S3Express support.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.4 (2023-11-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.3 (2023-11-27)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.2 (2023-11-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.1 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.0 (2023-11-17)

* **Feature**: **BREAKING CHANGE** Correct nullability of a large number of S3 structure fields. See https://github.com/aws/aws-sdk-go-v2/issues/2162.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.9 (2023-11-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.8 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.7 (2023-11-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.6 (2023-11-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.5 (2023-11-09.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.4 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.3 (2023-11-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.2 (2023-11-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.1 (2023-11-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.0 (2023-11-01)

* **Feature**: Adds support for configured endpoints via environment variables and the AWS shared configuration file.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.92 (2023-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.91 (2023-10-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.90 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.89 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.88 (2023-10-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.87 (2023-09-26)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.86 (2023-09-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.85 (2023-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.84 (2023-09-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.83 (2023-09-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.82 (2023-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.81 (2023-08-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.80 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.79 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.78 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.77 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.76 (2023-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.75 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.74 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.73 (2023-07-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.72 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.71 (2023-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.70 (2023-06-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.69 (2023-06-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.68 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.67 (2023-05-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.66 (2023-05-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.65 (2023-05-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.64 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.63 (2023-04-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.62 (2023-04-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.61 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.60 (2023-03-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.59 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.58 (2023-03-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.57 (2023-03-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.56 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.55 (2023-02-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.54 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.53 (2023-02-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.52 (2023-02-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.51 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.50 (2023-02-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.49 (2023-01-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.48 (2023-01-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.47 (2023-01-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.46 (2022-12-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.45 (2022-12-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.44 (2022-12-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.43 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.42 (2022-11-22)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.41 (2022-11-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.40 (2022-11-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.39 (2022-11-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.38 (2022-11-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.37 (2022-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.36 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.35 (2022-10-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.34 (2022-09-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.33 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.32 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.31 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.30 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.29 (2022-08-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.28 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.27 (2022-08-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.26 (2022-08-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.25 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.24 (2022-08-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.23 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.22 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.21 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.20 (2022-07-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.19 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.18 (2022-07-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.17 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.16 (2022-06-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.15 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.14 (2022-05-26)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.13 (2022-05-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.12 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.11 (2022-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.10 (2022-05-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.9 (2022-05-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.8 (2022-05-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.7 (2022-04-27)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.6 (2022-04-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.5 (2022-04-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.4 (2022-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.3 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.2 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.1 (2022-03-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.1 (2022-01-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.5 (2021-12-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.4 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.3 (2021-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.2 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.1 (2021-11-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.0 (2021-11-06)

* **Feature**: The SDK now supports configuration of FIPS and DualStack endpoints using environment variables, shared configuration, or programmatically.
* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.0 (2021-10-21)

* **Feature**: Updated  to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.4 (2021-10-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.3 (2021-09-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.2 (2021-09-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.1 (2021-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.0 (2021-08-27)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.1 (2021-08-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.0 (2021-08-04)

* **Feature**: adds error handling for defered close calls
* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.2 (2021-07-15)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.1 (2021-07-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.0 (2021-06-25)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.3 (2021-06-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.2 (2021-05-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.1 (2021-05-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.0 (2021-05-14)

* **Feature**: Constant has been added to modules to enable runtime version inspection for reporting.
* **Dependency Update**: Updated to the latest SDK module versions

