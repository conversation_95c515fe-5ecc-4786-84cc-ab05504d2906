# v1.16.9 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.8 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.7 (2024-06-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.6 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.5 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.4 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.3 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.2 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.1 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.16.0 (2024-03-21)

* **Feature**: Add config switch `DisableDefaultTimeout` that allows you to disable the default operation timeout (5 seconds) for IMDS calls.

# v1.15.4 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.3 (2024-03-07)

* **Bug Fix**: Remove dependency on go-cmp.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.2 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.1 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.15.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.11 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.10 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.9 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.8 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.7 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.6 (2023-11-28.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.5 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.4 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.3 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.2 (2023-11-02)

* No change notes available for this release.

# v1.14.1 (2023-11-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.14.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.13 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.12 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.11 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.10 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.9 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.8 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.7 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.6 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.5 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.4 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.3 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.2 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.1 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.13.0 (2023-03-14)

* **Feature**: Add flag to disable IMDSv1 fallback

# v1.12.24 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.23 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.22 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.21 (2022-12-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.20 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.19 (2022-10-24)

* **Bug Fix**: Fixes an issue that prevented logging of the API request or responses when the respective log modes were enabled.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.18 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.17 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.16 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.15 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.14 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.13 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.12 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.11 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.10 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.9 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.8 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.7 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.6 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.5 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.4 (2022-04-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.3 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.2 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.1 (2022-03-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.2 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.1 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.0 (2021-11-06)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.0 (2021-10-21)

* **Feature**: Updated  to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.0 (2021-10-11)

* **Feature**: Respect passed in Context Deadline/Timeout. Updates the IMDS Client operations to not override the passed in Context's Deadline or Timeout options. If an Client operation is called with a Context with a Deadline or Timeout, the client will no longer override it with the client's default timeout.
* **Bug Fix**: Fix IMDS client's response handling and operation timeout race. Fixes #1253
* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.1 (2021-09-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.0 (2021-08-27)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.1 (2021-08-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.0 (2021-08-04)

* **Feature**: adds error handling for defered close calls
* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.0 (2021-07-15)

* **Feature**: Support has been added for EC2 IPv6-enabled Instance Metadata Service Endpoints.
* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.0 (2021-06-25)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.1 (2021-05-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.0 (2021-05-14)

* **Feature**: Constant has been added to modules to enable runtime version inspection for reporting.
* **Dependency Update**: Updated to the latest SDK module versions

