# v1.3.13 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.12 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.11 (2024-06-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.10 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.9 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.8 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.7 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.6 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.5 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.4 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.3 (2024-03-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.2 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.1 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.10 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.9 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.8 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.7 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.6 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.5 (2023-11-28.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.4 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.3 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.2 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.1 (2023-11-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.43 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.42 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.41 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.40 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.39 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.38 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.37 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.36 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.35 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.34 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.33 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.32 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.31 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.30 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.29 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.28 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.27 (2022-12-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.26 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.25 (2022-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.24 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.23 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.22 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.21 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.20 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.19 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.18 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.17 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.16 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.15 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.14 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.13 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.12 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.11 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.10 (2022-04-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.9 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.8 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.7 (2022-03-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.6 (2022-03-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.5 (2022-02-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.4 (2022-01-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.3 (2022-01-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.2 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.1 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.0 (2021-11-06)

* **Feature**: The SDK now supports configuration of FIPS and DualStack endpoints using environment variables, shared configuration, or programmatically.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.7 (2021-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.6 (2021-10-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.5 (2021-09-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.4 (2021-08-27)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.3 (2021-08-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.2 (2021-08-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.1 (2021-07-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.0.0 (2021-06-25)

* **Release**: Release new modules
* **Dependency Update**: Updated to the latest SDK module versions

