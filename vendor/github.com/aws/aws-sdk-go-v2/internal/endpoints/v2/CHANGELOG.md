# v2.6.13 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.12 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.11 (2024-06-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.10 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.9 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.8 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.7 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.6 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.5 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.4 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.3 (2024-03-07)

* **Bug Fix**: Remove dependency on go-cmp.
* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.2 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.1 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.6.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.10 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.9 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.8 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.7 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.6 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.5 (2023-11-28.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.4 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.3 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.2 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.1 (2023-11-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.5.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.37 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.36 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.35 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.34 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.33 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.32 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.31 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.30 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.29 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.28 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.27 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.26 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.25 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.24 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.23 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.22 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.21 (2022-12-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.20 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.19 (2022-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.18 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.17 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.16 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.15 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.14 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.13 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.12 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.11 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.10 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.9 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.8 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.7 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.6 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.5 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.4 (2022-04-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.3 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.2 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.1 (2022-03-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.4.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v2.3.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v2.2.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v2.1.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v2.0.2 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.0.1 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v2.0.0 (2021-11-06)

* **Release**: Endpoint Variant Model Support
* **Feature**: The SDK now supports configuration of FIPS and DualStack endpoints using environment variables, shared configuration, or programmatically.
* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

