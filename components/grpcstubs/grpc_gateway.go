package grpcstubs

import (
	"context"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/OmniOrigin/projects-jnqx-video-extract/config"
)

type GatewayHandlerFunc struct {
	Method  string
	Path    string
	Handler runtime.HandlerFunc
}

type EndpointRegister func(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) error

type HandleFunc func(writer http.ResponseWriter, request *http.Request)

func HTTPServe(gRPCEndpoint *config.Endpoint, httpEndpoint *config.Endpoint, EndpointRegister EndpointRegister, handleFuncs map[string]HandleFunc) {
	serveMux := runtime.NewServeMux(
		runtime.WithMarshalerOption(
			runtime.MIMEWildcard,
			DefaultJSONPb,
		),
	)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := EndpointRegister(
		ctx, serveMux, gRPCEndpoint.String(), []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())},
	); err != nil {
		log.Fatal("failed to register gateway: ", err)
	}

	mux := http.NewServeMux()
	mux.Handle("/", serveMux)

	for path, handleFunc := range handleFuncs {
		mux.HandleFunc(path, handleFunc)
	}

	httpServer := http.Server{Addr: httpEndpoint.String(), Handler: mux}

	defer func() {
		if err := httpServer.Shutdown(ctx); err != nil {
			log.Warn(err)
		}
	}()

	log.Info("Try http server serve at ", httpEndpoint.String())
	if err := httpServer.ListenAndServe(); err != nil {
		log.Fatal("http server serve failed: ", err)
	}
}

func CORS(h http.Handler) http.Handler {
	return http.HandlerFunc(
		func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, PATCH, DELETE")
			w.Header().Set(
				"Access-Control-Allow-Headers",
				"Accept, Content-Type, Content-Length, Accept-Encoding, Authorization, ResponseType",
			)
			w.Header().Set("Vary", "Accept-Encoding, Origin")
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			h.ServeHTTP(w, r)
		},
	)
}

// OutgoingMatcher ...
func OutgoingMatcher(key string) (string, bool) {
	switch key {
	case "x-request-id":
		return "X-Request-Id", true
	default:
		return runtime.DefaultHeaderMatcher(key)
	}
}
