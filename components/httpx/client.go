package httpx

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
)

func SendRequest(url string, method string, body []byte) (*http.Response, error) {
	// 创建一个新的请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	// 创建一个新的 HTTP 客户端
	client := &http.Client{}

	// 使用客户端发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	// 检查 HTTP 响应状态码
	if resp.StatusCode != http.StatusOK {
		defer resp.Body.Close()
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, respBody)
	}

	return resp, nil
}
