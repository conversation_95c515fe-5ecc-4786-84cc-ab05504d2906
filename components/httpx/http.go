package httpx

import "net/http"

func NewClientWithBasic<PERSON>uth(username, password string) *http.Client {
	client := &http.Client{}
	client.Transport = &BasicAuthTransport{
		Username:  username,
		Password:  password,
		Transport: http.DefaultTransport,
	}
	return client
}

type BasicAuthTransport struct {
	Username  string
	Password  string
	Transport http.RoundTripper
}

func (b *BasicAuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.SetBasicAuth(b.Username, b.Password)
	return b.Transport.RoundTrip(req)
}
