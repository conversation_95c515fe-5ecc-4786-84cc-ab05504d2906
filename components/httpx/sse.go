package httpx

import (
	"net/http"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
)

var JSONPBMarshaler = &runtime.JSONPb{MarshalOptions: protojson.MarshalOptions{
	UseProtoNames:   true,
	EmitUnpopulated: true,
	UseEnumNumbers:  false,
}}

type Response[T master.ChatCompletionsResponse] struct {
	Err    error
	Result T
	Header http.Header
}

func StatusFromErr(err error) (s *status.Status) {
	s, ok := status.FromError(err)
	if !ok {
		return status.New(codes.Unknown, "unknow status")
	}
	return
}

// WriteChatCompletionsResponse writes a response.
func WriteChatCompletionsResponse(writer http.ResponseWriter, stream bool, response *Response[master.ChatCompletionsResponse]) {
	statusFromErr := StatusFromErr(response.Err)

	if response.Result.Status == nil && response.Err != nil {
		response.Result.Status = &master.Status{
			Code:    uint32(statusFromErr.Code()),
			Message: statusFromErr.Message(),
		}
	}

	statusCode := runtime.HTTPStatusFromCode(statusFromErr.Code())
	responseString, _ := JSONPBMarshaler.Marshal(&response.Result)

	write(writer, stream, statusCode, responseString)
}

func write(writer http.ResponseWriter, stream bool, statusCode int, body []byte) {
	if stream {
		writer.Header().Set("Content-Type", "text/event-stream")
		writer.Header().Set("Cache-Control", "no-cache")
		writer.Header().Set("Connection", "keep-alive")
	} else {
		writer.Header().Set("Content-Type", "application/json")
	}

	// 避免 http: superfluous response.WriteHeader call
	hasWriterHeader := writer.Header().Get("had_write_header")
	if hasWriterHeader != "1" {
		writer.WriteHeader(statusCode)
		writer.Header().Set("had_write_header", "1")
	}

	if stream {
		dataWithbody := append(append([]byte("data: "), body...), []byte("\n\n")...)
		_, _ = writer.Write(dataWithbody)
		writer.(http.Flusher).Flush()
	} else {
		_, _ = writer.Write(body)
	}
}
