package pktbase

import (
	"reflect"

	"github.com/pocketbase/pocketbase"
	"github.com/pocketbase/pocketbase/models"
	"github.com/pocketbase/pocketbase/models/schema"
	"github.com/pocketbase/pocketbase/tools/types"
	log "github.com/sirupsen/logrus"
)

func ImportCollection(app *pocketbase.PocketBase, table string, reflectType reflect.Type) error {
	if !app.Dao().IsCollectionNameUnique(table) {
		log.Debugf("collection %s already exists", table)
		return nil
	}

	var schemas []*schema.SchemaField
	for _, field := range reflect.VisibleFields(reflectType) {
		if field.Tag.Get("pocketbase") == "" {
			continue
		}
		schemas = append(schemas, &schema.SchemaField{
			Name:     field.Tag.Get("db"),
			Type:     field.Tag.Get("pocketbase"),
			Required: true,
		})
	}

	collection := &models.Collection{
		Name:       table,
		Type:       models.CollectionTypeBase,
		ListRule:   nil,
		ViewRule:   nil,
		CreateRule: nil,
		UpdateRule: nil,
		DeleteRule: nil,
		Schema:     schema.NewSchema(schemas...),
		Indexes:    types.JsonArray[string]{},
	}

	if err := app.Dao().SaveCollection(collection); err != nil {
		return err
	}

	log.Debugf("create collection[%s] ok", table)

	return nil
}
