syntax = "proto3";

option go_package = "github.com/OmniOrigin/projects-jnqx-video-extract/api/master_internal";

package jony4.projects.jnqx.video.extract.master_internal;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// swagger-ui base info,
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Master Internal",
    version: "1.0.0";
    description: "Master Internal API";
    contact: {
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>"
    };
    license: {
      name: "Apache 2.0",
      url: "http://www.apache.org/licenses/LICENSE-2.0.html"
    };
  },
  schemes: HTTP;
  consumes: "application/json";
  produces: "application/json";
  host: "127.0.0.1:8962";
};

// MasterService defines the service that handles tasks, media, preset questions, and media question answers.
// MasterService 定义了处理任务，媒体，预设问题和媒体问题答案的服务。
service MasterInternalService {
  // TaskUpdateStatus updates the status of a task.
  // TaskUpdateStatus 更新任务的状态。
  rpc TaskUpdateStatus (TaskUpdateStatusRequest) returns (TaskUpdateStatusResponse) {
    option (google.api.http) = {
      post: "/api/tasks/{id}/status"
    };
  }

  // 领取任务
  // Receive a task.
  rpc TaskReceive (TaskReceiveRequest) returns (TaskReceiveResponse) {
    option (google.api.http) = {
      post: "/api/tasks/receive"
    };
  }

  // MediaUpdate updates a media.
  // MediaUpdate 更新一个媒体。
  rpc MediaUpdate (Media) returns (Media) {
    option (google.api.http) = {
      put: "/api/medias/{id}"
    };
  }

  // DeviceRegister registers a device.
  // DeviceRegister 注册一个设备。
  rpc DeviceRegister (Device) returns (Device) {
    option (google.api.http) = {
      post: "/api/devices"
    };
  }

  // DevicePing pings a device.
  // DevicePing 对设备进行 ping 操作。
  rpc DevicePing (Device) returns (Device) {
    option (google.api.http) = {
      post: "/api/devices/{device_id}/ping"
    };
  }
}

// Media defines the structure of a media.
// Media 定义了媒体的结构。
message Media {
  string id = 100;
  string media_name = 1;
  MediaType type = 2;

  string source_url = 7;

  string transcription = 20;
  string lrc = 21;

  string vtt = 23;
  string graph = 25;
  string keywords = 26;
}

enum MediaType {
  MediaTypeVIDEO = 0;
  MediaTypeAUDIO = 1;
  MediaTypeIMAGE = 2;
  MediaTypeTEXT = 3;
  MediaTypePDF = 4;
  MediaTypeDOC = 5;
  MediaTypePPT = 6;
  MediaTypeXLS = 7;
  MediaTypeHTML = 8;
  MediaTypeMarkdown = 9;
  MediaTypeCSV = 10;
  MediaTypeEml = 11;
}

// PresetQuestion defines the structure of a preset question.
// PresetQuestion 定义了预设问题的结构。
message PresetQuestion {
  string id = 100;
  string question = 1;
  string prompt = 2;
}

// MediaQuestionAnswer defines the structure of a media question answer.
// TaskQuestionAnswer 定义了媒体问题答案的结构。
message TaskQuestionAnswer {
  string task_id = 1;
  string media_id = 2;
  string preset_question_id = 3;
  string question = 4;
  string answer = 5;
}

message MediaQuestionAnswersResponse {
  repeated TaskQuestionAnswer answers = 1;
}

// Device defines the structure of a device.
// Device 定义了设备的结构。
message Device {
  string name = 1;
  string device_id = 2;
  bool online = 3;
  int64 last_ping_time = 4;
}

enum TaskStatus {
  TaskStatusINIT = 0;
  TaskStatusRUNNING = 1;
  TaskStatusFINNISH = 2;
  TaskStatusERROR = 3;
}

// TaskStatusUpdateRequest is the request structure for updating the status of a task.
// TaskUpdateStatusRequest 是更新任务状态的请求结构。
message TaskUpdateStatusRequest {
  string id = 1;
  TaskStatus status = 2;
  string status_desc = 3;
}

// TaskStatusUpdateResponse is the response structure for updating the status of a task.
// TaskUpdateStatusResponse 是更新任务状态的响应结构。
message TaskUpdateStatusResponse {}

message TaskReceiveRequest {
  string device_id = 1;
}

message TaskReceiveResponse {
  Task task = 1;
}

message Task {
  string id = 100;
  string task_name = 1;
  string task_type = 2;
  string device_id = 3;
  string status = 4;
  string media_id = 5;
  string preset_question_ids = 6;
  int64 start_time = 10;
  int64 end_time = 11;
  string status_desc = 12;
  string model_name = 13;

  map<string, PresetQuestion> preset_questions = 20;
}

message MediaListRequest {}

message MediaListResponse {
  repeated Media medias = 1;
}