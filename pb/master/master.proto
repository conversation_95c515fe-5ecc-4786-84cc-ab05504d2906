syntax = "proto3";

option go_package = "github.com/OmniOrigin/projects-jnqx-video-extract/api/master";

package jony4.projects.jnqx.video.extract.master;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";


// swagger-ui base info,
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Master Public",
    version: "1.0.0";
    description: "Master Public API";
    contact: {
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>"
    };
    license: {
      name: "Apache 2.0",
      url: "http://www.apache.org/licenses/LICENSE-2.0.html"
    };
  },
  schemes: HTTP;
  consumes: "application/json";
  produces: "application/json";
  host: "127.0.0.1:8962";
};

// The MasterService service definition.
// 这个服务处理任务，媒体，预设问题和媒体问题答案。
service MasterService {
  // MediaCreate creates a new media.
  // MediaCreate 创建一个新的媒体。
  rpc MediaCreate (MediaCreateRequest) returns (MediaCreateResponse) {
    option (google.api.http) = {
      post: "/api/medias"
      body: "*"
    };
  }

  // MediaGet gets a media by its ID.
  // MediaGet 通过其 ID 获取一个媒体。
  rpc MediaGet (Media) returns (Media) {
    option (google.api.http) = {
      get: "/api/medias/{id}"
    };
  }

  // TaskCreate creates a new task.
  // TaskCreate 创建一个新的任务。
  rpc TaskCreate (Task) returns (Task) {
    option (google.api.http) = {
      post: "/api/tasks"
      body: "*"
    };
  }

  // TaskGet gets a task by its ID.
  // TaskGet 通过其 ID 获取一个任务。
  rpc TaskGet (TaskGetRequest) returns (Task) {
    option (google.api.http) = {
      get: "/api/tasks/{id}"
    };
  }

  // TaskDelete deletes a task.
  // TaskDelete 删除一个任务。
  rpc TaskDelete (TaskDeleteRequest) returns (TaskDeleteResponse) {
    option (google.api.http) = {
      delete: "/api/tasks/{id}"
    };
  }

  // TaskList lists all tasks.
  // TaskList 列出所有任务。
  rpc TaskList (TaskListRequest) returns (TaskListResponse) {
    option (google.api.http) = {
      get: "/api/tasks"
    };
  }

  // TaskRedo redoes a task.
  // TaskRedo 重做一个任务。
  rpc TaskRedo (Task) returns (TaskRedoResponse) {
    option (google.api.http) = {
      post: "/api/tasks/{id}/redo"
      body: "*"
    };
  }

  // TaskChatCompletions generates chat completions.
  // TaskChatCompletions 生成聊天完成。
  rpc TaskChatNormalCompletions (ChatCompletionsNormalRequest) returns (ChatCompletionsResponse) {
    option (google.api.http) = {
      post: "/api/v1/chat/completions"
      body: "*"
    };
  }
}

enum MediaType {
  MediaTypeVIDEO = 0;
  MediaTypeAUDIO = 1;
  MediaTypeIMAGE = 2;
  MediaTypeTEXT = 3;
  MediaTypePDF = 4;
  MediaTypeDOC = 5;
  MediaTypePPT = 6;
  MediaTypeXLS = 7;
  MediaTypeHTML = 8;
  MediaTypeMarkdown = 9;
  MediaTypeCSV = 10;
  MediaTypeEml = 11;
}

// MediaCreateRequest is the request structure for creating a media.
// MediaCreateRequest 是创建媒体的请求结构。
message MediaCreateRequest {
  string media_name = 1;
  // 媒体类型，默认为 MediaTypeVIDEO.
  MediaType type = 2;
  string source_url = 3;
}

// MediaCreateResponse is the response structure for creating a media.
// MediaCreateResponse 是创建媒体的响应结构。
message MediaCreateResponse {
  string id = 1;
  string media_name = 2;
  MediaType type = 3;
  string source_url = 4;
}

// Media defines the structure of a media.
// Media 定义了媒体的结构。
message Media {
  string id = 100;
  string media_name = 1;
  MediaType type = 2;
  string source_url = 7;
  string lrc = 21;
  string transcription = 22;
  string vtt = 23;
  repeated Transcript transcript = 24;
  string graph = 25;
  string keywords = 26;
}

message Transcript {
  string content = 1;
  int32 from = 2;
  int32 to = 3;
  string speaker = 4;
}

enum TaskType {
  TaskTypeVIDEO = 0;
  TaskTypeAUDIO = 1;
  TaskTypeTEXT = 2;
}

enum TaskStatus {
  TaskStatusINIT = 0;
  TaskStatusRUNNING = 1;
  TaskStatusFINNISH = 2;
  TaskStatusERROR = 3;
}

// Task defines the structure of a task.
// Task 定义了任务的结构。
message Task {
  string id = 100;
  // 【必填】任务名称
  string task_name = 1;
  // 【必填】任务类型，默认为 TaskTypeVIDEO.
  TaskType task_type = 2;
  // 【选填】设备 id
  string device_id = 3;
  // 【选填】任务状态，默认为 TaskStatusINIT.
  TaskStatus status = 4;
  // 【必填】媒体 id
  string media_id = 5;
  // 【选填】预设问题 id
  repeated string preset_question_ids = 6;
  // 【选填】开始时间
  int64 start_time = 10;
  // 【选填】结束时间
  int64 end_time = 11;
  // 【必填】回调地址
  string callback_url = 12;
  // 【选填】任务状态描述
  string status_desc = 13;
  // 【选填】使用的模型名称，默认走本地模型
  string model_name = 14;
}

// PresetQuestion defines the structure of a preset question.
// PresetQuestion 定义了预设问题的结构。
message PresetQuestion {
  string id = 100;
  // 【必填】问题
  string question = 1;
  // 【选填】程序自动生成问题的 prompt 内容，无需传入
  string prompt = 2;
}

// PresetQuestionDeleteResponse is the response structure for deleting a preset question.
// PresetQuestionDeleteResponse 是删除预设问题的响应结构。
message PresetQuestionDeleteResponse {}

// PresetQuestionListRequest is the request structure for listing preset questions.
// PresetQuestionListRequest 是列出预设问题的请求结构。
message PresetQuestionListRequest {}

// PresetQuestionListResponse is the response structure for listing preset questions.
// PresetQuestionListResponse 是列出预设问题的响应结构。
message PresetQuestionListResponse {
  repeated PresetQuestion questions = 1;
}

// MediaQuestionAnswer defines the structure of a media question answer.
// MediaQuestionAnswer 定义了媒体问题答案的结构。
message QuestionAnswer {
  string preset_question_id = 1;
  string question = 2;
  string answer = 3;
}

// TaskPresetQuestionAnswersRequest is the request structure for getting all preset question answers.
// TaskPresetQuestionAnswersRequest 是获取所有预设问题答案的请求结构。
message TaskPresetQuestionAnswersRequest {
  string task_id = 1;
}

// TaskPresetQuestionAnswersResponse is the response structure for getting all preset question answers.
// TaskPresetQuestionAnswersResponse 是获取所有预设问题答案的响应结构。
message TaskPresetQuestionAnswersResponse {
  repeated QuestionAnswer answers = 1;
}

// TaskListRequest is the request structure for listing tasks.
// TaskListRequest 是列出任务的请求结构。
message TaskListRequest {}

// TaskListResponse is the response structure for listing tasks.
// TaskListResponse 是列出任务的响应结构。
message TaskListResponse {
  repeated Task tasks = 1;
}

// TaskGetRequest is the request structure for getting a task.
// TaskGetRequest 是获取任务的请求结构。
message TaskGetRequest {
  string id = 1;
}

// TaskDeleteRequest is the request structure for deleting a task.
// TaskDeleteRequest 是删除任务的请求结构。
message TaskDeleteRequest {
  string id = 1;
}

// TaskDeleteResponse is the response structure for deleting a task.
// TaskDeleteResponse 是删除任务的响应结构。
message TaskDeleteResponse {}

// TaskRedoResponse is the response structure for redoing a task.
// TaskRedoResponse 是重做任务的响应结构。
message TaskRedoResponse {}

// ChatCompletionsRequest is the request structure for generating chat completions.
// ChatCompletionsRequest 是生成聊天完成的请求结构。
message ChatCompletionsRequest {
  // 输入给模型的对话上下文，数组中的每个对象为聊天的上下文信息。请注意，数组中最后一项必须为 user.
  repeated MessageWithRole messages = 1;
  // 是否使用流式传输, 默认false. 如果开启，数据将按照data-only SSE（server-sent events）返回中间结果，并以 data: [stop] 结束.
  bool stream = 2;
  // 【选填】使用的模型名称，默认走本地模型
  string model_name = 3;

  // 任务 ID
  string task_id = 10;
}

// ChatCompletionsRequest is the request structure for generating chat completions.
// ChatCompletionsRequest 是生成聊天完成的请求结构。
message ChatCompletionsNormalRequest {
  // 输入给模型的对话上下文，数组中的每个对象为聊天的上下文信息。请注意，数组中最后一项必须为 user.
  repeated MessageWithRole messages = 1;
  // 是否使用流式传输, 默认false. 如果开启，数据将按照data-only SSE（server-sent events）返回中间结果，并以 data: [stop] 结束.
  bool stream = 2;
  // 【选填】使用的模型名称，默认走本地模型
  string model_name = 3;
}

// MessageWithRole defines the structure of a message with role.
// MessageWithRole 定义了带角色的消息的结构。
message MessageWithRole {
  // 用户输入.
  string content = 1;
  // 消息作者的角色，枚举值.
  // system
  // user
  // assistant
  RoleType role = 2;
}

// RoleType defines the type of role.
// RoleType 定义了角色的类型。
enum RoleType {
  user = 0;
  assistant = 1;
  system = 2;
}

// ChatCompletionsResponse is the response structure for generating chat completions.
// ChatCompletionsResponse 是生成聊天完成的响应结构。
message ChatCompletionsResponse {
  message Choices {
    // 非流式请求时，生成的回复内容.
    string message = 1;
    // 流式请求时，生成的回复内容.
    string delta = 2;
    // finish_reason 停止生成的原因，枚举值.
    FinishReason finish_reason = 3;
  }

  message Usage {
    uint32 prompt_tokens = 1;
    uint32 completion_tokens = 2;
    uint32 total_tokens = 3;
  }

  message CompletionsData {
    string id = 1;
    repeated Choices choices = 2;
    Usage usage = 3;
  }

  CompletionsData data = 1;
  Status status = 2;
}

message Status {
  uint32 code = 1;
  string message = 2;
}


// finish_reason 停止生成的原因，枚举值.
// 默认非结束状态：none.
// 因结束符停止生成：stop.
// 因达到最大生成长度停止生成：length.
// 因触发敏感词停止生成： sensitive.
// 因触发模型上下文长度限制： context.
enum FinishReason {
  none = 0;
  stop = 1;
  length = 2;
  sensitive = 3;
  context = 4;
  function_call = 5;
  tool_calls = 6;
  content_filter = 7;
  null = 8;
}