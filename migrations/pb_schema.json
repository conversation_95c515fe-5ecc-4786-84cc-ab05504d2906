[{"id": "_pb_users_auth_", "name": "users", "type": "auth", "system": false, "schema": [{"system": false, "id": "users_name", "name": "name", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "users_avatar", "name": "avatar", "type": "file", "required": false, "presentable": false, "unique": false, "options": {"mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "maxSelect": 1, "maxSize": 5242880, "protected": false}}], "indexes": [], "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": null, "minPasswordLength": 8, "onlyEmailDomains": null, "onlyVerified": false, "requireEmail": false}}, {"id": "nv5rxf2ar5derq2", "name": "devices", "type": "base", "system": false, "schema": [{"system": false, "id": "5cfl<PERSON>cy", "name": "device_name", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "device_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "gntspv3e", "name": "online", "type": "bool", "required": false, "presentable": false, "unique": false, "options": {}}, {"system": false, "id": "llwkyxvp", "name": "last_ping_time", "type": "number", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}], "indexes": ["CREATE UNIQUE INDEX `idx_JgihiZt` ON `devices` (`device_id`)"], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "98kqf345i2gtlzm", "name": "media_question_answers", "type": "base", "system": false, "schema": [{"system": false, "id": "vp017ccv", "name": "media_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "6bzozcrm", "name": "preset_question_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "hj1cjqo7", "name": "question", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "qmbsthrn", "name": "answer", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "yt0kepg88jnau80", "name": "media_transcriptions", "type": "base", "system": false, "schema": [{"system": false, "id": "gmikvnd7", "name": "media_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "e2qqeurl", "name": "from", "type": "number", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "cuqbdjgz", "name": "to", "type": "number", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "tbwwxrem", "name": "content", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "ipg8wdo0", "name": "keywords", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "l2pyhtvz", "name": "speaker", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "wy22ug3ig5b2ml8", "name": "medias", "type": "base", "system": false, "schema": [{"system": false, "id": "dzmi5zru", "name": "media_name", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "zhlc2cbt", "name": "type", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "nhtegkpx", "name": "bucket", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "hbxxczrw", "name": "key", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "i4fprrci", "name": "parent_bucket", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "wjez4hlp", "name": "parent_key", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "efsfyttt", "name": "source_url", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "lsmx5aer", "name": "download_url", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "j3rytlsp", "name": "next_transform_type", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "pvwmdn7o", "name": "next_transform_bucket", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "7apto8ao", "name": "next_transform_key", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "ku6dyqc3", "name": "transcription", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "t2q0eu4l", "name": "lrc", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "b3hvdxaa", "name": "knowledge_base_id", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "q1z14mzks6fyv6f", "name": "preset_questions", "type": "base", "system": false, "schema": [{"system": false, "id": "vwtaauzd", "name": "question", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "vndb9vew", "name": "prompt", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "pqvfla0t5tiyd0h", "name": "task_question_answers", "type": "base", "system": false, "schema": [{"system": false, "id": "rwtdrvh3", "name": "task_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "jgjffkps", "name": "media_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "gzigvyue", "name": "preset_question_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "2ktnx91v", "name": "question", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "zaxaqfiq", "name": "answer", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "qv97brz697c9qxu", "name": "tasks", "type": "base", "system": false, "schema": [{"system": false, "id": "8kbakzbe", "name": "task_name", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "xbllwtf6", "name": "task_type", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "mda2iwz3", "name": "device_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "2dqkkyaa", "name": "status", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "lc1b210x", "name": "media_id", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "8sozxc2d", "name": "preset_question_ids", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "8iyypsxi", "name": "start_time", "type": "number", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "xinizmcy", "name": "end_time", "type": "number", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "myrd45z0", "name": "callback_url", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}]