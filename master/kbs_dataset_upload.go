package master

import (
	"errors"
	"io"
	"net/http"

	"github.com/jony4/vclient/ragflow/document"
	"github.com/labstack/echo/v5"
	log "github.com/sirupsen/logrus"
)

func (m *Master) KBsUpload(ctx echo.Context) error {
	datasetId := ctx.PathParam("dataset_id")
	file, err := ctx.FormFile("file")
	if err != nil {
		return err
	}
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	uploadHeader := http.Header{}
	uploadHeader.Set("Authorization", "Bearer "+m.config.RAGFlowEndpoint.Password)
	uploadResp, err := m.vclient.Ragflow().DocumentUpload().
		Header(uploadHeader).
		DatasetID(datasetId).
		HostAddr(m.config.RAGFlowEndpoint.String()).
		Files(
			map[string]io.Reader{
				file.Filename: io.NopCloser(src),
			},
		).Do(ctx.Request().Context())
	if err != nil {
		log.Errorf("upload document failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	if uploadResp.Code != 0 {
		log.Errorf("upload document failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, errors.New("upload document failed"))
	}
	if uploadResp.Data == nil || len(uploadResp.Data) == 0 {
		log.Errorf("upload document failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, errors.New("upload document failed"))
	}
	var fileId string
	if len(uploadResp.Data) == 1 {
		if err = m.vclient.Ragflow().DocumentParse().Init(
			&document.ParseRequest{
				DatasetId: datasetId,
				DocumentIDs: []string{
					uploadResp.Data[0].Id,
				},
			},
			&document.ParseResponse{},
			uploadHeader,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("parse document failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		fileId = uploadResp.Data[0].Id
	}

	return OK(ctx, map[string]interface{}{
		"document_id": fileId,
	})
}
