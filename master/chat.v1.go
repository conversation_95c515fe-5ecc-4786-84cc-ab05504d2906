package master

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master"
	"github.com/OmniOrigin/projects-jnqx-video-extract/components/httpx"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/sashabaranov/go-openai"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
)

// type ChatCompletionsRequest openai.ChatCompletionNewParams
//
// type ChatCompletionsResponse *openai.ChatCompletion
//
// type stream *ssestream.Stream[openai.ChatCompletionChunk]

// todo 根据不同模型调整不同的 prompt
// 另外需要根据不同模型，调整不同的温度、频率惩罚、TopP 等参数
var systemPrompt = `您是一个专注于课堂内容学习的智能助手，您的任务是帮助学生根据课堂视频转录内容提取知识点、总结要点，并进行高质量的问答和翻译指导。请始终以教学场景为中心，避免与课堂内容无关的话题。在提供帮助时，确保您的回答准确、简洁且与课堂内容高度相关。

**具体任务指导**：

1. **知识点提取**  
   - 根据课堂内容，提炼出核心知识点，列出关键概念、公式或观点。  
   - 如果适用，可对知识点进行简要说明或分类。

2. **内容总结**  
   - 使用简洁明了的语言总结课堂内容，确保涵盖主要知识点和关键信息。  
   - 如果内容较长，可以按逻辑段落进行分段总结。

3. **问答支持**  
   - 对学生提出的问题，结合上下文准确作答。  
   - 如果问题有多个可能答案，请根据上下文提供最相关的答案，并简要解释理由。  
   - 引导学生深入思考，例如提问“这与课堂中的哪个知识点相关？”或“是否有其他可能的解答方式？”。

4. **翻译指导**  
   - 根据学生的需求，帮助将课堂内容翻译成目标语言（如中英互译）。  
   - 在翻译过程中，尽量保持术语准确和语义清晰，同时兼顾语言的流畅性。  
   - 提供翻译技巧，例如：  
     - 如何处理复杂句式和学术术语。  
     - 如何保留原文语气和专业性。  
     - 针对生词或术语的翻译，提供适当解释或注释。  
   - 鼓励学生主动翻译部分内容，并提供反馈和改进建议。

5. **辅助学习**  
   - 提供学习建议，例如如何进一步深入理解某个知识点，或推荐可能的学习步骤。  
   - 如果学生出现理解或翻译偏差，耐心解释并纠正。

**注意事项**：  
- 始终围绕课堂内容，避免涉及非教学相关的话题。  
- 语言表达清晰简洁，适合学生理解。  
- 鼓励学生独立思考，但必要时提供详细的解释或提示。  
- 如果课堂内容有不明确或不完整的地方，请提醒学生查阅原始视频或相关资料。
`

// ChatCompletions stream bool
func (m *Master) ChatCompletions(writer http.ResponseWriter, req *http.Request) {
	// 读取 body 并赋值到变量中
	b, err := io.ReadAll(req.Body)
	if err != nil {
		log.Errorf("read request body failed: %v", err)
		httpx.WriteChatCompletionsResponse(writer, false, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.Internal, "read request body failed: %v", err),
		})
		return
	}
	// 读取 request body 并解析到 ChatConversationsRequest 结构体中
	request := &master.ChatCompletionsNormalRequest{}
	jsonpb := &runtime.JSONPb{UnmarshalOptions: protojson.UnmarshalOptions{DiscardUnknown: true}}
	if err := jsonpb.Unmarshal(b, request); err != nil {
		log.Errorf("decode request body failed: %v", err)
		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.InvalidArgument, "decode request body failed: %v", err),
		})
		return
	}

	messages := []openai.ChatCompletionMessage{}
	var (
		isSetSystemPrompt      bool
		isSetSystemPromptValue = systemPrompt
	)
	for _, message := range request.Messages {
		if message.Role == master.RoleType_system {
			isSetSystemPrompt = true
			isSetSystemPromptValue = message.Content
		}
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    message.Role.String(),
			Content: message.Content,
		})
	}
	if !isSetSystemPrompt {
		messages = append([]openai.ChatCompletionMessage{
			{
				Role:    master.RoleType_system.String(),
				Content: isSetSystemPromptValue,
			},
		}, messages...)
	}

	if messages[len(messages)-1].Role != master.RoleType_user.String() {
		log.Errorf("last message role is not user")
		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.InvalidArgument, "last message role is not user"),
		})
		return
	}

	openaiClient, err := m.openaiClient(request.ModelName)
	if err != nil {
		log.Errorf("openaiClient failed: %v", err)
		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.Internal, "openaiClient failed: %v", err),
		})
		return
	}

	chatRequest := openai.ChatCompletionRequest{
		Model:            request.ModelName,
		Messages:         messages,
		Temperature:      0.7,
		Stream:           true,
		MaxTokens:        m.config.Models.LLMMaxTokens,
		FrequencyPenalty: 1.1,
		TopP:             0.7,
	}
	if request.ModelName == "deepseek-chat" {
		chatRequest.Model = m.config.Models.CurrentModel
		chatRequest.MaxTokens = m.config.Models.LLMMaxTokens
		chatRequest.FrequencyPenalty = 0
		chatRequest.TopP = 1
		chatRequest.PresencePenalty = 0
		chatRequest.Temperature = 1
	}

	if !request.Stream {
		chatRequest.Stream = false

		resp, err := openaiClient.CreateChatCompletion(req.Context(), chatRequest)
		if err != nil {
			log.Errorf("CreateChatCompletion failed: %v", err)
			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: status.Errorf(codes.Internal, "CreateChatCompletion failed: %v", err),
			})
			return
		}
		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: nil,
			Result: master.ChatCompletionsResponse{
				Data: &master.ChatCompletionsResponse_CompletionsData{
					Id: fmt.Sprintf("%d", resp.Choices[0].Index),
					Choices: []*master.ChatCompletionsResponse_Choices{
						{
							Message:      resp.Choices[0].Message.Content,
							Delta:        "",
							FinishReason: master.FinishReason(master.FinishReason_value[string(resp.Choices[0].FinishReason)]),
						},
					},
					Usage: &master.ChatCompletionsResponse_Usage{
						PromptTokens:     uint32(resp.Usage.PromptTokens),
						CompletionTokens: uint32(resp.Usage.CompletionTokens),
						TotalTokens:      uint32(resp.Usage.TotalTokens),
					},
				},
				Status: &master.Status{Code: 0, Message: "success"},
			},
			Header: map[string][]string{},
		})
		return
	}

	chatBody, _ := json.Marshal(chatRequest)
	log.Infof("chatBody: %s", string(chatBody))

	stream, err := openaiClient.CreateChatCompletionStream(req.Context(), chatRequest)
	if err != nil {
		log.Errorf("CreateChatCompletionStream failed: %v", err)
		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.Internal, "CreateChatCompletionStream failed: %v", err),
		})
		return
	}
	defer stream.Close()

	for {
		select {
		case <-req.Context().Done():
			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: status.Errorf(codes.Canceled, "request context done"),
			})
			log.Debugf("c.Request().Context().Done()")
			return
		default:
			response, err := stream.Recv()
			if err != nil && !errors.Is(err, io.EOF) {
				log.Errorf("stream.Recv ErrorWriter, err=%+v", err)
				return
			} else if errors.Is(err, io.EOF) {
				log.Infof("stream EOF")
				return
			}
			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: nil,
				Result: master.ChatCompletionsResponse{
					Data: &master.ChatCompletionsResponse_CompletionsData{
						Id: fmt.Sprintf("%d", response.Choices[0].Index),
						Choices: []*master.ChatCompletionsResponse_Choices{
							{
								Message:      "",
								Delta:        response.Choices[0].Delta.Content,
								FinishReason: master.FinishReason(master.FinishReason_value[string(response.Choices[0].FinishReason)]),
							},
						},
						Usage: &master.ChatCompletionsResponse_Usage{
							PromptTokens:     0,
							CompletionTokens: 0,
							TotalTokens:      0,
						},
					},
					Status: &master.Status{Code: 0, Message: "success"},
				},
				Header: map[string][]string{},
			})
		}
	}
}
