package master

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"

	"github.com/labstack/echo/v5"
	log "github.com/sirupsen/logrus"
)

type Resp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func OKWriter(writer http.ResponseWriter, data interface{}) {
	writer.Header().Set("Content-Type", "application/json")
	writer.WriteHeader(http.StatusOK)

	resp := &Resp{
		Code:    0,
		Message: "ok",
		Data:    data,
	}

	if err := json.NewEncoder(writer).Encode(resp); err != nil {
		// Handle JSON encoding error
		http.Error(writer, "Failed to encode response", http.StatusInternalServerError)
	}
}

// ErrorWriter writes an error response (HTTP 500 by default)
func ErrorWriter(w http.ResponseWriter, err error) {
	ErrorWithStatus(w, err, http.StatusInternalServerError)
}

// ErrorWithStatus writes an error response with custom status code
func ErrorWithStatus(w http.ResponseWriter, err error, status int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	resp := &Resp{
		Code:    500,
		Message: err.Error(),
		Data:    nil,
	}

	if encodeErr := json.NewEncoder(w).Encode(resp); encodeErr != nil {
		http.Error(w, "Failed to encode error response", http.StatusInternalServerError)
	}
}

func OK(ctx echo.Context, data interface{}) error {
	dataBytes, _ := json.Marshal(data)
	log.Infof("Response: %v", string(dataBytes))

	resp := echo.Map{
		"code":    0,
		"message": "success",
		"data":    data,
	}

	return ctx.JSON(http.StatusOK, resp)
}

func Error(ctx echo.Context, code int, err error) error {
	// Capture caller information
	_, file, line, _ := runtime.Caller(1)
	callerInfo := fmt.Sprintf("%s:%d", file, line)

	resp := echo.Map{
		"code": code,
		"data": nil,
	}

	if err != nil {
		log.Errorf("Response: %v (caller: %s)", err.Error(), callerInfo)
		resp["message"] = err.Error()
	}

	dataBytes, _ := json.Marshal(resp)
	log.Infof("Response: %v", string(dataBytes))

	return ctx.JSON(code, resp)
}
