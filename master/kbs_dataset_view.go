package master

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/jony4/vclient/ragflow/dataset"
	"github.com/jony4/vclient/ragflow/document"
	"github.com/labstack/echo/v5"
	log "github.com/sirupsen/logrus"
)

func (m *Master) KBsView(ctx echo.Context) error {
	datasetId := ctx.PathParam("dataset_id")
	page := ctx.QueryParamDefault("page", "1")
	pageInt, _ := strconv.Atoi(page)
	pageSize := ctx.QueryParamDefault("page_size", "30")
	pageSizeInt, _ := strconv.Atoi(pageSize)

	ctx.Request().Header.Set("Authorization", "Bearer "+m.config.RAGFlowEndpoint.Password)

	datasetListResp := &dataset.ListResponse{}
	if err := m.vclient.Ragflow().DatasetList().Init(
		&dataset.ListRequest{
			ID: datasetId,
		},
		datasetListResp,
		ctx.Request().<PERSON>er,
	).Do(ctx.Request().Context()); err != nil {
		log.Errorf("list dataset failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	if datasetListResp.Code == 102 {
		log.Errorf("dataset not found: %v", datasetListResp.Message)
		return Error(ctx, http.StatusNotFound, errors.New(datasetListResp.Message))
	}
	var datasetInfo dataset.Info
	if datasetListResp.Code == 0 {
		if len(datasetListResp.Data) == 1 {
			datasetInfo = datasetListResp.Data[0]
		} else {
			log.Errorf("dataset not found: %v", datasetListResp.Message)
			return Error(ctx, http.StatusNotFound, errors.New(datasetListResp.Message))
		}
	}

	docListResp := &document.ListResponse{}
	if err := m.vclient.Ragflow().DocumentList().Init(
		&document.ListRequest{
			Page:      pageInt,
			PageSize:  pageSizeInt,
			DatasetId: datasetId,
		},
		docListResp,
		ctx.Request().Header,
	).Do(ctx.Request().Context()); err != nil {
		log.Errorf("list document failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	return OK(ctx, map[string]interface{}{
		"dataset":   datasetInfo,
		"documents": docListResp.Data,
	})
}
