package master

// func (m *Master) HandleChatCompletions(writer http.ResponseWriter, req *http.Request) {
// 	// 读取 body 并赋值到变量中
// 	b, err := io.ReadAll(req.Body)
// 	if err != nil {
// 		log.Errorf("read request body failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, false, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.Internal, "read request body failed: %v", err),
// 		})
// 		return
// 	}
// 	// 读取 request body 并解析到 ChatConversationsRequest 结构体中
// 	request := &master.ChatCompletionsRequest{}
// 	jsonpb := &runtime.JSONPb{UnmarshalOptions: protojson.UnmarshalOptions{DiscardUnknown: true}}
// 	if err := jsonpb.Unmarshal(b, request); err != nil {
// 		log.Errorf("decode request body failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.InvalidArgument, "decode request body failed: %v", err),
// 		})
// 		return
// 	}
//
// 	// 根据 request.TaskId 读取任务信息
// 	taskInfo, err := m.TaskGet(req.Context(), &master.TaskGetRequest{Id: request.TaskId})
// 	if err != nil {
// 		log.Errorf("get task info failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.Internal, "get task info failed: %v", err),
// 		})
// 		return
// 	}
//
// 	// 根据 task.MediaId 读取媒体信息
// 	mediaInfo, err := m.MediaGet(req.Context(), &master.Media{Id: taskInfo.MediaId})
// 	if err != nil {
// 		log.Errorf("get media info failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.Internal, "get media info failed: %v", err),
// 		})
// 		return
// 	}
//
// 	if len(request.Messages) == 0 {
// 		log.Errorf("消息长度为 0: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.InvalidArgument, "消息长度为 0: %v", err),
// 		})
// 		return
// 	}
//
// 	messages := []openai.ChatCompletionMessage{
// 		{
// 			Role:    "system",
// 			Content: "你是一位爱学习、爱提问的好学生，会根据课堂内容进行各种问题提问，请仔细阅读课堂内容，并根据问题进行回答。",
// 		},
// 	}
// 	for _, message := range request.Messages {
// 		if message.Role == master.RoleType_system {
// 			log.Errorf("get task info failed: %v", err)
// 			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 				Err: status.Errorf(codes.Internal, "System 消息暂不支持设置 %v", err),
// 			})
// 			return
// 		}
// 		messages = append(messages, openai.ChatCompletionMessage{
// 			Role:    message.Role.String(),
// 			Content: message.Content,
// 		})
// 	}
//
// 	if messages[len(messages)-1].Role != master.RoleType_user.String() {
// 		log.Errorf("last message role is not user")
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.InvalidArgument, "last message role is not user"),
// 		})
// 		return
// 	}
//
// 	LastMessageContent := request.GetMessages()[len(request.GetMessages())-1]
//
// 	if len(mediaInfo.Lrc) > 23*1024 {
// 		mediaInfo.Lrc = mediaInfo.Lrc[:23*1024]
// 	}
//
// 	if len(mediaInfo.Lrc) == 0 {
// 		log.Errorf("mediaInfo.Lrc is empty")
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.InvalidArgument, "mediaInfo.Lrc is empty, 请判断任务是否失败"),
// 		})
// 		return
// 	} else {
// 		messages[len(messages)-1].Content = fmt.Sprintf(`
// context 中的内容是课堂转录内容，采用 lrc 格式存储，每行为一句话，每句话的开始时间和结束时间用 [] 括起来，请你将老师授课内容用时间线联系，并根据用户的问题，仔细思考后回答用户的问题。回答过程中有以下几点：
// 1. 回答请用中文，不要用英文。
// 2. 回答内容要有逻辑，不要随意回答。
// 3. 回答内容要有条理，不要一团乱。
// 4. 回答内容要有深度，不要浅尝辄止。
// 5. 回答内容要有广度，不要局限于一个方面。
// 6. 回答内容要有细节及对转录内容的引用，不要泛泛而谈。
// 7. 如果问题是比较宽泛的比如“你好”“你是谁”等，可以忽视掉 context 内容
// 8. 如果问题是比较具体的比如“课堂上老师讲了哪些“知识要点”？”等，需要根据 context 内容进行回答，回答时需要引用 context 内容，比如“老师在课堂上讲了...”。
// 9. 如果问题不是宽泛类的，但 context 中又找不到合适的上下文，请直接回复“在视频中没有找到相关内容”。
//
// 下面是课堂转录内容:
// <context>
// %s
// </context>
//
// 现在请你回答用户的问题：%s
// `, mediaInfo.Lrc, LastMessageContent.GetContent())
// 	}
//
// 	openaiClient, err := m.openaiClient(request.ModelName)
// 	if err != nil {
// 		log.Errorf("openaiClient failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.Internal, "openaiClient failed: %v", err),
// 		})
// 		return
// 	}
//
// 	if !request.Stream {
// 		resp, err := openaiClient.CreateChatCompletion(req.Context(), openai.ChatCompletionRequest{
// 			Model:            request.ModelName,
// 			Messages:         messages,
// 			Temperature:      0.5,
// 			Stream:           false,
// 			MaxTokens:        m.config.Models.LLMMaxTokens,
// 			FrequencyPenalty: 1.1,
// 			TopP:             0.7,
// 		})
// 		if err != nil {
// 			log.Errorf("CreateChatCompletion failed: %v", err)
// 			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 				Err: status.Errorf(codes.Internal, "CreateChatCompletion failed: %v", err),
// 			})
// 			return
// 		}
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: nil,
// 			Result: master.ChatCompletionsResponse{
// 				Data: &master.ChatCompletionsResponse_CompletionsData{
// 					Id: fmt.Sprintf("%d", resp.Choices[0].Index),
// 					Choices: []*master.ChatCompletionsResponse_Choices{
// 						{
// 							Message:      resp.Choices[0].Message.Content,
// 							Delta:        "",
// 							FinishReason: master.FinishReason(master.FinishReason_value[string(resp.Choices[0].FinishReason)]),
// 						},
// 					},
// 					Usage: &master.ChatCompletionsResponse_Usage{
// 						PromptTokens:     uint32(resp.Usage.PromptTokens),
// 						CompletionTokens: uint32(resp.Usage.CompletionTokens),
// 						TotalTokens:      uint32(resp.Usage.TotalTokens),
// 					},
// 				},
// 				Status: &master.Status{Code: 0, Message: "success"},
// 			},
// 			Header: map[string][]string{},
// 		})
// 		return
// 	}
//
// 	chatRequest := openai.ChatCompletionRequest{
// 		Model:            request.ModelName,
// 		Messages:         messages,
// 		Temperature:      0.7,
// 		Stream:           true,
// 		MaxTokens:        m.config.Models.LLMMaxTokens,
// 		FrequencyPenalty: 1.1,
// 		TopP:             0.7,
// 		// Stop: []string{
// 		// 	"<|assistant|>",
// 		// },
// 	}
//
// 	chatBody, _ := json.Marshal(chatRequest)
// 	log.Infof("chatBody: %s", string(chatBody))
//
// 	stream, err := openaiClient.CreateChatCompletionStream(req.Context(), chatRequest)
// 	if err != nil {
// 		log.Errorf("CreateChatCompletionStream failed: %v", err)
// 		httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 			Err: status.Errorf(codes.Internal, "CreateChatCompletionStream failed: %v", err),
// 		})
// 		return
// 	}
// 	defer stream.Close()
//
// 	for {
// 		select {
// 		case <-req.Context().Done():
// 			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 				Err: status.Errorf(codes.Canceled, "request context done"),
// 			})
// 			log.Debugf("c.Request().Context().Done()")
// 			return
// 		default:
// 			response, err := stream.Recv()
// 			if err != nil && !errors.Is(err, io.EOF) {
// 				log.Errorf("stream.Recv ErrorWriter, err=%+v", err)
// 				return
// 			} else if errors.Is(err, io.EOF) {
// 				// httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 				// 	Err: status.Errorf(codes.OKWriter, "stream EOF"),
// 				// })
// 				log.Infof("stream EOF")
// 				return
// 			}
// 			httpx.WriteChatCompletionsResponse(writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
// 				Err: nil,
// 				Result: master.ChatCompletionsResponse{
// 					Data: &master.ChatCompletionsResponse_CompletionsData{
// 						Id: fmt.Sprintf("%d", response.Choices[0].Index),
// 						Choices: []*master.ChatCompletionsResponse_Choices{
// 							{
// 								Message:      "",
// 								Delta:        response.Choices[0].Delta.Content,
// 								FinishReason: master.FinishReason(master.FinishReason_value[string(response.Choices[0].FinishReason)]),
// 							},
// 						},
// 						Usage: &master.ChatCompletionsResponse_Usage{
// 							PromptTokens:     0,
// 							CompletionTokens: 0,
// 							TotalTokens:      0,
// 						},
// 					},
// 					Status: &master.Status{Code: 0, Message: "success"},
// 				},
// 				Header: map[string][]string{},
// 			})
// 		}
// 	}
// }
