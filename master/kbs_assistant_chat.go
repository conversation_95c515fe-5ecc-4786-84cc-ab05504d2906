package master

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master"
	"github.com/OmniOrigin/projects-jnqx-video-extract/components/httpx"
	"github.com/jony4/vclient/ragflow/assistant"
	"github.com/labstack/echo/v5"
	"github.com/sashabaranov/go-openai"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type KBsChatCompletionsRequest struct {
	Messages  []*MessageWithRole `json:"messages"`
	Stream    bool               `json:"stream"`
	ModelName string             `json:"model_name"`
}

type MessageWithRole struct {
	Content string `json:"content"`
	Role    string `json:"role"`
}

const (
	RoleTypeUser      = "user"
	RoleTypeAssistant = "assistant"
	RoleTypeSystem    = "system"
)

// KBsChatCompletions stream bool
func (m *Master) KBsChatCompletions(ctx echo.Context) error {
	request := &KBsChatCompletionsRequest{}
	if err := ctx.Bind(request); err != nil {
		return Error(ctx, http.StatusBadRequest, err)
	}
	if request.ModelName == "" {
		return Error(ctx, http.StatusBadRequest, errors.New("model_name is empty"))
	}

	assistantId := ctx.PathParam("assistant_id")

	var (
		messages               = []openai.ChatCompletionMessage{}
		isSetSystemPrompt      bool
		isSetSystemPromptValue = `你是一个人工智能助手。`
	)
	for _, message := range request.Messages {
		if message.Role == RoleTypeSystem {
			isSetSystemPrompt = true
			isSetSystemPromptValue = message.Content
		}
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    message.Role,
			Content: message.Content,
		})
		break
	}
	if !isSetSystemPrompt {
		messages = append([]openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: isSetSystemPromptValue,
			},
		}, messages...)
	}

	if messages[len(messages)-1].Role != master.RoleType_user.String() {
		log.Errorf("last message role is not user")
		httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.InvalidArgument, "last message role is not user"),
		})
		return nil
	}

	ctx.Request().Header.Set("Authorization", fmt.Sprintf("Bearer %s", m.config.RAGFlowEndpoint.Password))
	ragflowMessages := make([]assistant.ChatCompletionMessage, 0, len(messages))
	for _, message := range messages {
		ragflowMessages = append(ragflowMessages, assistant.ChatCompletionMessage{
			Role:    message.Role,
			Content: message.Content,
		})
	}

	chatReq := &assistant.ChatRequest{
		Model:       request.ModelName,
		Messages:    ragflowMessages,
		Stream:      false,
		AssistantId: assistantId,
	}

	if !request.Stream {
		resp := &assistant.ChatResponse{}
		if err := m.vclient.Ragflow().AssistantChat().Init(
			chatReq,
			resp,
			ctx.Request().Header,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("assistant chat failed: %v", err)
			httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: status.Errorf(codes.Internal, "assistant chat failed: %v", err),
			})
			return nil
		}

		httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: nil,
			Result: master.ChatCompletionsResponse{
				Data: &master.ChatCompletionsResponse_CompletionsData{
					Id: fmt.Sprintf("%d", resp.Choices[0].Index),
					Choices: []*master.ChatCompletionsResponse_Choices{
						{
							Message:      resp.Choices[0].Message.Content,
							Delta:        "",
							FinishReason: master.FinishReason(master.FinishReason_value[string(resp.Choices[0].FinishReason)]),
						},
					},
					Usage: &master.ChatCompletionsResponse_Usage{
						PromptTokens:     uint32(resp.Usage.PromptTokens),
						CompletionTokens: uint32(resp.Usage.CompletionTokens),
						TotalTokens:      uint32(resp.Usage.TotalTokens),
					},
				},
				Status: &master.Status{Code: 0, Message: "success"},
			},
			Header: map[string][]string{},
		})
		return nil
	}

	chatRequest := openai.ChatCompletionRequest{
		Model:    request.ModelName,
		Messages: messages,
		Stream:   true,
	}
	chatBody, _ := json.Marshal(chatRequest)
	log.Infof("chatBody: %s", string(chatBody))

	// openaiClient, err := m.ragflowOpenAIClient(assistantId)
	// if err != nil {
	// 	log.Errorf("openaiClient failed: %v", err)
	// 	httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
	// 		Err: status.Errorf(codes.Internal, "openaiClient failed: %v", err),
	// 	})
	// 	return nil
	// }

	chatReq.Stream = true

	stream, err := m.vclient.Ragflow().AssistantChatStream().Request(chatReq).Header(ctx.Request().Header).AssistantId(assistantId).Do(ctx.Request().Context())
	if err != nil {
		log.Errorf("CreateChatCompletionStream failed: %v", err)
		httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
			Err: status.Errorf(codes.Internal, "CreateChatCompletionStream failed: %v", err),
		})
		return nil
	}

	for {
		select {
		case <-ctx.Request().Context().Done():
			httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: status.Errorf(codes.Canceled, "request context done"),
			})
			log.Debugf("c.Request().Context().Done()")
			return nil
		default:
			response, ok := <-stream
			if !ok {
				httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
					Err: status.Errorf(codes.Canceled, "not ok"),
				})
				return nil
			}
			if response == nil {
				httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
					Err: status.Errorf(codes.Canceled, "response is nil"),
				})
				return nil
			}
			if response.ID == "" {
				return nil
			}
			httpx.WriteChatCompletionsResponse(ctx.Response().Writer, request.Stream, &httpx.Response[master.ChatCompletionsResponse]{
				Err: nil,
				Result: master.ChatCompletionsResponse{
					Data: &master.ChatCompletionsResponse_CompletionsData{
						Id: fmt.Sprintf("%d", response.Choices[0].Index),
						Choices: []*master.ChatCompletionsResponse_Choices{
							{
								Message:      "",
								Delta:        response.Choices[0].Delta.Content,
								FinishReason: master.FinishReason(master.FinishReason_value[string(response.Choices[0].FinishReason)]),
							},
						},
						Usage: &master.ChatCompletionsResponse_Usage{
							PromptTokens:     0,
							CompletionTokens: 0,
							TotalTokens:      0,
						},
					},
					Status: &master.Status{Code: 0, Message: "success"},
				},
				Header: map[string][]string{},
			})
		}
	}
}
