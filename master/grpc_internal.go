package master

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"
	"time"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master_internal"
	"github.com/OmniOrigin/projects-jnqx-video-extract/components/httpx"
	"github.com/jony4/vclient/funasr"
	"github.com/pocketbase/dbx"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (m *Master) TaskUpdateStatus(ctx context.Context, task *master_internal.TaskUpdateStatusRequest) (*master_internal.TaskUpdateStatusResponse, error) {
	if task.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "Id is empty")
	}
	modelTask := &Task{}
	if err := m.app.Dao().ModelQuery(&Task{}).Where(dbx.HashExp{"id": task.Id}).One(modelTask); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}
	modelTask.Status = task.Status.String()
	modelTask.StatusDesc = task.StatusDesc
	if task.Status == master_internal.TaskStatus_TaskStatusFINNISH {
		modelTask.EndTime = time.Now().Unix()
	}
	if err := m.app.Dao().Save(modelTask); err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	if task.Status == master_internal.TaskStatus_TaskStatusFINNISH {
		go func() {
			modelTaskBytes, _ := json.Marshal(modelTask)
			for i := 0; i < 5; i++ {
				time.Sleep(2 * time.Second)
				resp, err := httpx.SendRequest(modelTask.CallbackURL, "POST", modelTaskBytes)
				if err != nil {
					log.Errorf("SendRequest failed: %v", err)
					continue
				}
				body, err := io.ReadAll(resp.Body)
				if err != nil {
					log.Errorf("io.ReadAll failed: %v", err)
					resp.Body.Close()
					continue
				}
				log.Infof("SendRequest response: %s", string(body))
				resp.Body.Close()
				break
			}
		}()
	}

	return &master_internal.TaskUpdateStatusResponse{}, nil
}

func (m *Master) TaskReceive(ctx context.Context, request *master_internal.TaskReceiveRequest) (*master_internal.TaskReceiveResponse, error) {
	// 查询设备ID为空的任务
	tasks := make([]*Task, 0)
	if err := m.app.Dao().ModelQuery(&Task{}).Where(dbx.HashExp{"status": master_internal.TaskStatus_TaskStatusINIT.String()}).All(&tasks); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 如果没有找到任务，返回错误
	if len(tasks) == 0 {
		return &master_internal.TaskReceiveResponse{Task: nil}, nil
	}

	// 按任务创建时间进行升序排序
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].StartTime < tasks[j].StartTime
	})

	// 选择最早创建的任务
	task := tasks[0]

	// 更新任务的设备ID
	task.DeviceId = request.DeviceId

	// 保存更新后的任务到数据库
	if err := m.app.Dao().Save(task); err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	internalTask := convertModelTask2ProtoInternalTask(task)

	presetQuestionIds := strings.Split(task.PresetQuestionIds, ",")
	presetQuestionIdsMapping := map[string]bool{}
	for _, presetQuestionId := range presetQuestionIds {
		if presetQuestionId == "" {
			continue
		}
		if ok, _ := presetQuestionIdsMapping[presetQuestionId]; !ok {
			presetQuestionIdsMapping[presetQuestionId] = true
		}
	}

	// 返回任务
	return &master_internal.TaskReceiveResponse{Task: internalTask}, nil
}

func convertModelTask2ProtoInternalTask(task *Task) *master_internal.Task {
	protoTask := &master_internal.Task{
		Id:                task.Id,
		TaskName:          task.TaskName,
		TaskType:          task.TaskType,
		DeviceId:          task.DeviceId,
		Status:            task.Status,
		MediaId:           task.MediaId,
		PresetQuestionIds: task.PresetQuestionIds,
		StartTime:         task.StartTime,
		EndTime:           task.EndTime,
		StatusDesc:        task.StatusDesc,
		PresetQuestions:   make(map[string]*master_internal.PresetQuestion),
	}

	return protoTask
}

func convertModelMedia2ProtoMedia(media *Media) *master_internal.Media {
	protoMedia := &master_internal.Media{
		Id:            media.Id,
		MediaName:     media.MediaName,
		Type:          master_internal.MediaType(master_internal.MediaType_value[media.Type]),
		SourceUrl:     media.SourceURL,
		Transcription: media.Transcription,
		Lrc:           media.Lrc,
		Vtt:           media.Vtt,
		Graph:         media.KnowledgeGraph,
		Keywords:      media.Keywords,
	}

	return protoMedia
}

func (m *Master) MediaUpdate(ctx context.Context, media *master_internal.Media) (*master_internal.Media, error) {
	if media.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "Id is empty")
	}
	modelMedia := &Media{}
	if err := m.app.Dao().ModelQuery(&Media{}).Where(dbx.HashExp{"id": media.Id}).One(modelMedia); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}
	modelMedia.MediaName = media.MediaName
	modelMedia.Type = media.Type.String()
	modelMedia.SourceURL = media.SourceUrl
	modelMedia.Lrc = media.Lrc
	modelMedia.Vtt = media.Vtt
	modelMedia.Transcription = media.Transcription

	trans := &funasr.RecognitionResponse{}
	if err := json.Unmarshal([]byte(media.Transcription), trans); err != nil {
		log.Errorf("Unmarshal failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	graphRes, err := m.ChatGraph(ctx, trans.Output[0].Text)
	if err != nil {
		log.Errorf("生成graph 图错误 %v", err)
	}
	if graphRes != "" {
		modelMedia.KnowledgeGraph = graphRes
	}

	keywords, err := m.ChatKeywords(ctx, trans.Output[0].Text)
	if err != nil {
		log.Errorf("ChatKeywords 错误 %v", err)
	}
	if len(keywords) > 0 {
		modelMedia.Keywords = strings.Join(keywords, ",")
	}

	if err = m.app.Dao().Save(modelMedia); err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	for _, out := range trans.Output {
		for _, si := range out.SentenceInfo {
			mediaTrans := &MediaTranscription{
				MediaId:  media.Id,
				From:     int32(si.Start),
				To:       int32(si.End),
				Content:  si.Text,
				Keywords: "",
				Speaker:  fmt.Sprintf("%d", si.Spk),
			}
			if err := m.app.Dao().Save(mediaTrans); err != nil {
				log.Errorf("Save failed: %v", err)
				return nil, status.Error(codes.Internal, err.Error())
			}
		}
	}

	return convertModelMedia2ProtoMedia(modelMedia), nil
}

func (m *Master) DeviceRegister(ctx context.Context, device *master_internal.Device) (*master_internal.Device, error) {
	// 验证设备名是否为空
	if device.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "Name is empty")
	}

	// 验证设备ID是否为空
	if device.DeviceId == "" {
		return nil, status.Error(codes.InvalidArgument, "DeviceId is empty")
	}

	// 查询数据库中是否已经存在该设备
	modelDevice := &Device{}
	if err := m.app.Dao().ModelQuery(&Device{}).Where(dbx.HashExp{"device_id": device.DeviceId}).One(modelDevice); err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, status.Error(codes.Internal, err.Error())
	} else if errors.Is(err, sql.ErrNoRows) {
		// 如果数据库中不存在该设备，则注册新的设备
		modelDevice = &Device{
			DeviceName: device.Name,
			DeviceID:   device.DeviceId,
			Online:     true, // 设备注册时默认为在线状态
		}
		if err := m.app.Dao().Save(modelDevice); err != nil {
			return nil, status.Error(codes.Internal, err.Error())
		}
	} else {
		// 如果数据库中已经存在该设备，则更新设备的在线状态
		modelDevice.Online = true
		if err := m.app.Dao().Save(modelDevice); err != nil {
			return nil, status.Error(codes.Internal, err.Error())
		}
	}

	// 返回新注册的设备
	return device, nil
}

func (m *Master) DevicePing(ctx context.Context, device *master_internal.Device) (*master_internal.Device, error) {
	// 验证设备ID是否为空
	if device.DeviceId == "" {
		return nil, status.Error(codes.InvalidArgument, "DeviceId is empty")
	}

	// 从数据库中获取设备
	modelDevice := &Device{}
	if err := m.app.Dao().ModelQuery(&Device{}).Where(dbx.HashExp{"device_id": device.DeviceId}).One(modelDevice); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 更新设备的在线状态和最后心跳时间
	modelDevice.Online = true
	modelDevice.LastPingTime = time.Now().Unix()

	// 保存更新后的设备到数据库
	err := m.app.Dao().Save(modelDevice)
	if err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	device.Online = modelDevice.Online
	device.LastPingTime = modelDevice.LastPingTime

	// 返回更新后的设备
	return device, nil
}
