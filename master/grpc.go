package master

import (
	"context"
	"strings"
	"time"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master"
	"github.com/google/uuid"
	"github.com/pocketbase/dbx"
	"github.com/pocketbase/pocketbase/models"
	"github.com/pocketbase/pocketbase/tools/list"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (m *Master) TaskCreate(ctx context.Context, task *master.Task) (*master.Task, error) {
	if task.MediaId == "" {
		return nil, status.Error(codes.InvalidArgument, "MediaId is empty")
	}
	if task.CallbackUrl == "" {
		return nil, status.Error(codes.InvalidArgument, "CallbackUrl is empty")
	}
	if err := m.app.Dao().ModelQuery(&Media{}).Where(dbx.HashExp{"id": task.MediaId}).One(&Media{}); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.InvalidArgument, "MediaId is invalid")
	}
	if err := m.app.Dao().ModelQuery(&PresetQuestion{}).Where(dbx.In("id", list.ToInterfaceSlice(task.PresetQuestionIds)...)).All(&[]PresetQuestion{}); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.InvalidArgument, "PresetQuestionIds is invalid")
	}
	modelTask := &Task{
		BaseModel: models.BaseModel{
			Id: uuid.NewString(),
		},
		TaskName:    task.TaskName,
		TaskType:    task.TaskType.String(),
		DeviceId:    "", // 在任务创建时，设备ID为空，在领取任务时候，根据传入的 device id 进行分配；在任务重做时候，重新置空，由 worker 自动领取任务
		Status:      master.TaskStatus_TaskStatusINIT.String(),
		StatusDesc:  "init",
		MediaId:     task.MediaId,
		StartTime:   time.Now().Unix(),
		EndTime:     0,
		CallbackURL: task.CallbackUrl,
		ModelName:   task.ModelName,
	}
	err := m.app.Dao().Save(modelTask)
	if err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}
	return convertModelTask2ProtoTask(modelTask), nil
}

func convertModelTask2ProtoTask(task *Task) *master.Task {
	protoTask := &master.Task{
		Id:                task.Id,
		TaskName:          task.TaskName,
		TaskType:          master.TaskType(master.TaskType_value[task.TaskType]),
		DeviceId:          task.DeviceId,
		Status:            master.TaskStatus(master.TaskStatus_value[task.Status]),
		StatusDesc:        task.StatusDesc,
		MediaId:           task.MediaId,
		PresetQuestionIds: strings.Split(task.PresetQuestionIds, ","),
		StartTime:         task.StartTime,
		EndTime:           task.EndTime,
		CallbackUrl:       task.CallbackURL,
		ModelName:         task.ModelName,
	}

	return protoTask
}

func (m *Master) TaskList(ctx context.Context, request *master.TaskListRequest) (*master.TaskListResponse, error) {
	tasks := make([]*Task, 0)
	if err := m.app.Dao().ModelQuery(&Task{}).All(&tasks); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	protoTasks := make([]*master.Task, 0)
	for _, task := range tasks {
		protoTasks = append(protoTasks, convertModelTask2ProtoTask(task))
	}

	return &master.TaskListResponse{
		Tasks: protoTasks,
	}, nil
}

func (m *Master) TaskGet(ctx context.Context, request *master.TaskGetRequest) (*master.Task, error) {
	if request.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "Id is empty")
	}
	task := &Task{}
	if err := m.app.Dao().ModelQuery(&Task{}).Where(dbx.HashExp{"id": request.Id}).One(task); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	return convertModelTask2ProtoTask(task), nil
}

func (m *Master) TaskDelete(ctx context.Context, request *master.TaskDeleteRequest) (*master.TaskDeleteResponse, error) {
	// 验证任务ID是否为空
	if request.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "Id is empty")
	}

	// 从数据库中获取任务
	modelTask := &Task{}
	if err := m.app.Dao().ModelQuery(&Task{}).Where(dbx.HashExp{"id": request.Id}).One(modelTask); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 从数据库中删除任务
	err := m.app.Dao().Delete(modelTask)
	if err != nil {
		log.Errorf("Delete failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 返回成功的响应
	return &master.TaskDeleteResponse{}, nil
}

func (m *Master) TaskRedo(ctx context.Context, task *master.Task) (*master.TaskRedoResponse, error) {
	// 根据任务ID查找任务
	modelTask := &Task{}
	if err := m.app.Dao().ModelQuery(&Task{}).Where(dbx.HashExp{"id": task.Id}).One(modelTask); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 检查任务的状态，如果任务的状态不是成功状态，则将任务状态置为初始状态，并将设备ID置为空
	// if modelTask.Status != master.TaskStatus_TaskStatusFINNISH.String() {
	modelTask.Status = master.TaskStatus_TaskStatusINIT.String()
	modelTask.StatusDesc = "redo"
	modelTask.DeviceId = ""
	modelTask.EndTime = 0
	// }

	// 保存更新后的任务到数据库
	if err := m.app.Dao().Save(modelTask); err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 删除任务相关的问题答案
	result, err := m.app.Dao().DB().NewQuery("DELETE FROM task_question_answers WHERE task_id = {:task_id}").Bind(
		dbx.Params{"task_id": task.Id},
	).Execute()
	if err != nil {
		log.Errorf("Delete failed: %v", err)
		return nil, err
	}
	log.Infof("Delete result: %v", result)

	// 返回成功的响应
	return &master.TaskRedoResponse{}, nil
}

func (m *Master) MediaCreate(ctx context.Context, media *master.MediaCreateRequest) (*master.MediaCreateResponse, error) {
	// media.Bucket = m.config.Minio.BucketName
	// media.Key = fmt.Sprintf(keyFormat, media.Type, media.Id)
	modelMedia := &Media{
		BaseModel: models.BaseModel{
			Id: uuid.NewString(),
		},
		MediaName: media.MediaName,
		Type:      media.Type.String(),
		SourceURL: media.SourceUrl,
	}
	err := m.app.Dao().Save(modelMedia)
	if err != nil {
		log.Errorf("Save failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &master.MediaCreateResponse{
		Id:        modelMedia.GetId(),
		MediaName: media.MediaName,
		Type:      media.Type,
		SourceUrl: media.SourceUrl,
	}, nil
}

// MediaGet gets a media by its ID.
// MediaGet 通过其 ID 获取一个媒体。
func (m *Master) MediaGet(ctx context.Context, media *master.Media) (*master.Media, error) {
	if media.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "Id is empty")
	}
	modelMedia := &Media{}
	if err := m.app.Dao().ModelQuery(&Media{}).Where(dbx.HashExp{"id": media.Id}).One(modelMedia); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	protoMedia := convertModelMedia2MasterProtoMedia(modelMedia)

	mediaTrans := make([]*MediaTranscription, 0)
	if err := m.app.Dao().ModelQuery(&MediaTranscription{}).Where(dbx.HashExp{"media_id": media.Id}).OrderBy(
		"from ASC",
	).All(&mediaTrans); err != nil {
		log.Errorf("ModelQuery failed: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}

	protoMedia.Transcript = make([]*master.Transcript, 0)
	for _, mt := range mediaTrans {
		protoMedia.Transcript = append(protoMedia.Transcript, &master.Transcript{
			From:    mt.From,
			To:      mt.To,
			Content: mt.Content,
			Speaker: mt.Speaker,
		})
	}

	return protoMedia, nil
}

func (m *Master) TaskChatNormalCompletions(ctx context.Context, request *master.ChatCompletionsNormalRequest) (*master.ChatCompletionsResponse, error) {
	return nil, status.Error(codes.InvalidArgument, "只支持 http 方式")
}

func convertModelMedia2MasterProtoMedia(media *Media) *master.Media {
	protoMedia := &master.Media{
		Id:            media.Id,
		MediaName:     media.MediaName,
		Type:          master.MediaType(master.MediaType_value[media.Type]),
		SourceUrl:     media.SourceURL,
		Transcription: media.Transcription,
		Lrc:           media.Lrc,
		Vtt:           media.Vtt,
		Graph:         media.KnowledgeGraph,
		Keywords:      media.Keywords,
	}

	return protoMedia
}
