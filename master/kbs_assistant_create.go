package master

import (
	"errors"
	"net/http"

	"github.com/jony4/vclient/ragflow/assistant"
	"github.com/jony4/vclient/ragflow/dataset"
	"github.com/jony4/vclient/ragflow/document"
	"github.com/labstack/echo/v5"
	log "github.com/sirupsen/logrus"
)

type KBsAssistantCreateReq struct {
	Name       string   `json:"name"`
	DatasetIds []string `json:"dataset_ids"`
}

type KBsAssistantCreateResp struct {
	AssistantId    string `json:"assistant_id"`
	AssistantName  string `json:"assistant_name"`
	AssistantModel string `json:"assistant_model"`
}

func (m *Master) KBsAssistantCreate(ctx echo.Context) error {
	var kbsReq = new(KBsAssistantCreateReq)
	if err := ctx.Bind(kbsReq); err != nil {
		return Error(ctx, http.StatusBadRequest, err)
	}
	if kbsReq.Name == "" {
		log.Errorf("kb assistant name is empty")
		return Error(ctx, http.StatusBadRequest, errors.New("kb assistant name is empty"))
	}
	if len(kbsReq.DatasetIds) == 0 {
		log.Errorf("kb assistant dataset ids is empty")
		return Error(ctx, http.StatusBadRequest, errors.New("kb assistant dataset ids is empty"))
	}

	ctx.Request().Header.Set("Authorization", "Bearer "+m.config.RAGFlowEndpoint.Password)

	for _, datasetId := range kbsReq.DatasetIds {
		datasetListResp := &dataset.ListResponse{}
		if err := m.vclient.Ragflow().DatasetList().Init(
			&dataset.ListRequest{
				ID: datasetId,
			},
			datasetListResp,
			ctx.Request().Header,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("list dataset failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if datasetListResp.Code == 102 {
			log.Errorf("dataset not found: %v", datasetListResp.Message)
			return Error(ctx, http.StatusNotFound, errors.New(datasetListResp.Message))
		}
		docListResp := &document.ListResponse{}
		if err := m.vclient.Ragflow().DocumentList().Init(
			&document.ListRequest{
				Page:       1,
				PageSize:   0,
				OrderBy:    "",
				Desc:       false,
				Keywords:   "",
				DocumentID: "",
				Name:       "hello.txt",
				DatasetId:  datasetId,
			},
			docListResp,
			ctx.Request().Header,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("list document failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if docListResp.Code != 0 {
			log.Errorf("list document failed: %v", docListResp.Message)
			return Error(ctx, http.StatusInternalServerError, errors.New(docListResp.Message))
		}
	}

	assistantListResp := &assistant.ListResponse{}
	if err := m.vclient.Ragflow().AssistantList().Init(
		&assistant.ListRequest{
			Name: kbsReq.Name,
		},
		assistantListResp,
		ctx.Request().Header,
	).Do(ctx.Request().Context()); err != nil {
		log.Errorf("list assistant failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	var (
		currentAssistantId    string
		currentAssistantName  string
		currentAssistantModel string
	)
	if assistantListResp.Code != 0 {
		assistantResp := &assistant.CreateResponse{}
		if err := m.vclient.Ragflow().AssistantCreate().Init(
			&assistant.CreateRequest{
				Name:       kbsReq.Name,
				DatasetIDs: kbsReq.DatasetIds,
				Prompt: &assistant.ChatAssistantPrompt{
					EmptyResponse: "这个问题小智还不懂，我也会努力学习的！",
					Opener:        "你好！我是你的人工智能助手小智，请问我可以帮你做点什么呢？",
					ShowQuote:     false,
				},
			},
			assistantResp,
			ctx.Request().Header,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("create assistant failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if assistantResp.Code != 0 {
			log.Error("create assistant failed")
			return Error(ctx, http.StatusInternalServerError, errors.New("create assistant failed"))
		}
		if assistantResp.Data == nil || assistantResp.Data.ID == "" {
			log.Error("create assistant failed")
			return Error(ctx, http.StatusInternalServerError, errors.New("create assistant failed"))
		}
		currentAssistantId = assistantResp.Data.ID
		currentAssistantName = assistantResp.Data.Name
		currentAssistantModel = assistantResp.Data.LLM.ModelName
	} else if assistantListResp.Code == 0 {
		if len(assistantListResp.Data) == 1 {
			currentAssistantId = assistantListResp.Data[0].Id
			currentAssistantName = assistantListResp.Data[0].Name
			currentAssistantModel = assistantListResp.Data[0].Llm.ModelName
		}
	}

	resp := &KBsAssistantCreateResp{
		AssistantId:    currentAssistantId,
		AssistantName:  currentAssistantName,
		AssistantModel: currentAssistantModel,
	}
	return OK(ctx, resp)
}
