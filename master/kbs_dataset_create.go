package master

import (
	_ "embed"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/jony4/vclient/ragflow/dataset"
	"github.com/jony4/vclient/ragflow/document"
	"github.com/labstack/echo/v5"
	log "github.com/sirupsen/logrus"
)

//go:embed hello.txt
var helloTxt string

type KBsCreateReq struct {
	Name string `json:"name"`
}

type KBsCreateResp struct {
	DatasetId   string `json:"dataset_id"`
	DatasetName string `json:"dataset_name"`
}

func (m *Master) KBsCreate(ctx echo.Context) error {
	var kbsReq = new(KBsCreateReq)
	if err := ctx.Bind(kbsReq); err != nil {
		return Error(ctx, http.StatusBadRequest, err)
	}
	if kbsReq.Name == "" {
		log.Errorf("kb name is empty")
		return Error(ctx, http.StatusBadRequest, errors.New("kb name is empty"))
	}
	ctx.Request().Header.Set("Authorization", "Bearer "+m.config.RAGFlowEndpoint.Password)

	datasetListResp := &dataset.ListResponse{}
	if err := m.vclient.Ragflow().DatasetList().Init(
		&dataset.ListRequest{
			Name: kbsReq.Name,
		},
		datasetListResp,
		ctx.Request().Header,
	).Do(ctx.Request().Context()); err != nil {
		log.Errorf("list dataset failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	var (
		currentDatasetId   string
		currentDatasetName string
	)
	if datasetListResp.Code == 102 {
		datasetResp := &dataset.CreateResponse{}
		datasetReq := dataset.DefaultCreateRequest()
		datasetReq.Name = kbsReq.Name
		if err := m.vclient.Ragflow().DatasetCreate().Init(
			datasetReq,
			datasetResp,
			ctx.Request().Header,
		).Do(ctx.Request().Context()); err != nil {
			log.Errorf("create dataset failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if datasetResp.Data == nil || datasetResp.Data.ID == "" {
			log.Error("create dataset failed")
			return Error(ctx, http.StatusInternalServerError, errors.New("create dataset failed"))
		}
		currentDatasetId = datasetResp.Data.ID
		currentDatasetName = datasetResp.Data.Name
	} else if datasetListResp.Code == 0 {
		if len(datasetListResp.Data) == 1 {
			currentDatasetId = datasetListResp.Data[0].ID
			currentDatasetName = datasetListResp.Data[0].Name
		}
	}

	if currentDatasetId == "" {
		log.Errorf("dataset id is empty")
		return Error(ctx, http.StatusInternalServerError, errors.New("dataset id is empty"))
	}

	// 1. 先检查数据集中是否 parse 好了文档。
	docListResp := &document.ListResponse{}
	if err := m.vclient.Ragflow().DocumentList().Init(
		&document.ListRequest{
			Page:       1,
			PageSize:   0,
			OrderBy:    "",
			Desc:       false,
			Keywords:   "",
			DocumentID: "",
			Name:       "hello.txt",
			DatasetId:  currentDatasetId,
		},
		docListResp,
		ctx.Request().Header,
	).Do(ctx.Request().Context()); err != nil {
		log.Errorf("list document failed: %v", err)
		return Error(ctx, http.StatusInternalServerError, err)
	}
	if docListResp.Code != 0 {
		uploadHeader := http.Header{}
		uploadHeader.Set("Authorization", "Bearer "+m.config.RAGFlowEndpoint.Password)
		uploadResp, err := m.vclient.Ragflow().DocumentUpload().
			Header(uploadHeader).
			DatasetID(currentDatasetId).
			HostAddr(m.config.RAGFlowEndpoint.String()).
			Files(
				map[string]io.Reader{
					"hello.txt": strings.NewReader(helloTxt),
				},
			).Do(ctx.Request().Context())
		if err != nil {
			log.Errorf("upload document failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if uploadResp.Code != 0 {
			log.Errorf("upload document failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, err)
		}
		if uploadResp.Data == nil || len(uploadResp.Data) == 0 {
			log.Errorf("upload document failed: %v", err)
			return Error(ctx, http.StatusInternalServerError, errors.New("upload document failed"))
		}
		if len(uploadResp.Data) == 1 {
			if err = m.vclient.Ragflow().DocumentParse().Init(
				&document.ParseRequest{
					DatasetId: currentDatasetId,
					DocumentIDs: []string{
						uploadResp.Data[0].Id,
					},
				},
				&document.ParseResponse{},
				ctx.Request().Header,
			).Do(ctx.Request().Context()); err != nil {
				log.Errorf("parse document failed: %v", err)
				return Error(ctx, http.StatusInternalServerError, err)
			}

			time.Sleep(10 * time.Second)
		}
	}

	resp := &KBsCreateResp{
		DatasetId:   currentDatasetId,
		DatasetName: currentDatasetName,
	}
	return OK(ctx, resp)
}
