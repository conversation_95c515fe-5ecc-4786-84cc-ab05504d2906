package master

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sashabaranov/go-openai"
	log "github.com/sirupsen/logrus"
)

const ModelDeepSeek = "deepseek-chat"
const ModelInstanceDS = "ep-20250218124100-v5ttj"

const (
	graphPrompt = `<context>"%s"</context>

请将上面context中的内容，用 plantuml 格式来表达成思维导图，要求如下：
1. 要有结构层级
2. 层级尽可能多，至少 5 层结构
3. 提取的关键信息尽可能准确无误，可以适当延伸补充词汇但不宜过多，原则上是要贴近原文。
4. 每个节点尽可能不少于 3 个词
5. 直接返回 plantuml 的代码，以@startmindmap @endmindmap为标志，无需解释原因
6. 内容中不要包含《》书名号，因为解析不了
7. 如果单节点的子节点数量过多，比如超过 10 个，或者子节点的内容过长，比如超过 20 个字，请将其分为多个节点，避免单个节点过于复杂

补充信息：
在PlantUML 中，MindMap 图表是头脑风暴、组织想法和项目规划的有效工具。MindMap 图表或思维导图是信息的可视化表示，其中的中心思想分支成相关主题，形成一个概念的蜘蛛网。PlantUML 以其简单、基于文本的语法为创建这些图表提供了便利，从而可以高效地组织复杂的想法并将其可视化。

请开始任务
`
)

func (m *Master) ChatGraph(ctx context.Context, trans string) (string, error) {
	request := openai.ChatCompletionRequest{
		Stream:    false,
		MaxTokens: m.config.Models.LLMMaxTokens,
		// FrequencyPenalty: 1.1,
		TopP:        1,
		Model:       ModelInstanceDS,
		Temperature: 0,

		Messages: []openai.ChatCompletionMessage{},
	}

	userQuestion := openai.ChatCompletionMessage{
		Role:    "user",
		Content: fmt.Sprintf(graphPrompt, trans),
	}

	request.Messages = append(request.Messages, userQuestion)

	// debug log request string
	requestBytes, _ := json.Marshal(request)
	log.Debugf("CreateChatCompletion request: %s\n", string(requestBytes))

	openaiClient, err := m.openaiClient(ModelInstanceDS)
	if err != nil {
		log.Errorf("openaiClient failed: %v", err)
		return "", err
	}

	resp, err := openaiClient.CreateChatCompletion(ctx, request)
	if err != nil {
		log.Errorf("CreateChatCompletion failed with %s\n", err)
		return "", err
	}
	log.Debugf("CreateChatCompletion response: %v\n", resp)

	res := resp.Choices[0].Message.Content

	res = strings.TrimLeft(res, "plantuml")
	res = strings.TrimLeft(res, "```")
	res = removeAfterBackticks(res)
	res = strings.ReplaceAll(res, "《", " ")
	res = strings.ReplaceAll(res, "》", " ")
	res = strings.TrimLeft(res, "plantuml")

	return res, nil
}

func removeAfterBackticks(input string) string {
	idx := strings.Index(input, "```")
	if idx != -1 {
		return input[:idx] // 截取 ``` 之前的部分
	}
	return input
}

func (m *Master) ChatKeywords(ctx context.Context, input string) ([]string, error) {
	request := openai.ChatCompletionRequest{
		Stream:    false,
		MaxTokens: m.config.Models.LLMMaxTokens,
		// FrequencyPenalty: 1.1,
		TopP:        1,
		Model:       ModelInstanceDS,
		Temperature: 0,

		Messages: []openai.ChatCompletionMessage{},
	}

	keywordsPrompt := `
<context>
%s
</context>

以上为一堂大学课程录课的音频转文字内容，请帮我提取出转录内容的关键词，要求如下：
1. 转录内容受限于 asr 转录精度，会存在 百分之五 左右的不准确度，请忽视转录错误的内容
2. 直接从文本内容中摘要关键词，不要新造
3. 直接输出 json 格式的关键词数组，方便后续程序处理，示例 json 如下 ["关键词一", "关键词二"]

请开始任务
`

	userQuestion := openai.ChatCompletionMessage{
		Role:    "user",
		Content: fmt.Sprintf(keywordsPrompt, input),
	}

	request.Messages = append(request.Messages, userQuestion)

	// debug log request string
	requestBytes, _ := json.Marshal(request)
	log.Debugf("CreateChatCompletion request: %s\n", string(requestBytes))

	openaiClient, err := m.openaiClient(ModelInstanceDS)
	if err != nil {
		log.Errorf("openaiClient failed: %v", err)
		return nil, err
	}

	resp, err := openaiClient.CreateChatCompletion(ctx, request)
	if err != nil {
		log.Errorf("CreateChatCompletion failed with %s\n", err)
		return nil, err
	}
	log.Debugf("CreateChatCompletion response: %v\n", resp)

	res := resp.Choices[0].Message.Content
	res = strings.TrimLeft(res, "```json")
	res = strings.TrimRight(res, "```")

	keywords := make([]string, 0)
	if err := json.Unmarshal([]byte(res), &keywords); err != nil {
		log.Errorf("json.Unmarshal failed with %s\n", err)
		return nil, err
	}

	return keywords, nil
}
