package master

import "github.com/pocketbase/pocketbase/models"

type Task struct {
	models.BaseModel
	// Name 任务名称
	TaskName string `db:"task_name" json:"task_name" pocketbase:"text"`
	// 任务类型
	TaskType string `db:"task_type" json:"task_type" pocketbase:"text"`

	DeviceId   string `db:"device_id" json:"device_id" pocketbase:"text"`
	Status     string `db:"status" json:"status" pocketbase:"text"`
	StatusDesc string `db:"status_desc" json:"status_desc" pocketbase:"text"`

	// 一个任务对应一个媒体 id
	MediaId string `db:"media_id" json:"media_id" pocketbase:"text"`
	// 预设问题组，逗号分割
	PresetQuestionIds string `db:"preset_question_ids" json:"preset_question_ids" pocketbase:"text"`

	StartTime int64 `db:"start_time" json:"start_time" pocketbase:"number"`
	EndTime   int64 `db:"end_time" json:"end_time" pocketbase:"number"`

	CallbackURL string `db:"callback_url" json:"callback_url" pocketbase:"text"`
	ModelName   string `db:"model_name" json:"model_name" pocketbase:"text"`
}

func (t *Task) TableName() string {
	return "tasks"
}

type Media struct {
	models.BaseModel
	// Name 媒体名称
	MediaName string `db:"media_name" json:"media_name" pocketbase:"text"`
	// Type 媒体类型
	Type string `db:"type" json:"type" pocketbase:"text"`
	// SourceURL 来源地址
	SourceURL string `db:"source_url" json:"source_url" pocketbase:"text"`

	// 字幕原始文件，JSON
	Transcription string `db:"transcription" json:"transcription" pocketbase:"text"`
	// 字幕原始文件，lrc
	Lrc            string `db:"lrc" json:"lrc" pocketbase:"text"`
	Vtt            string `db:"vtt" json:"vtt" pocketbase:"text"`
	Keywords       string `db:"keywords" json:"keywords" pocketbase:"text"`
	KnowledgeGraph string `db:"knowledge_graph" json:"knowledge_graph" pocketbase:"text"`
}

func (m *Media) TableName() string {
	return "medias"
}

// MediaTranscription 媒体字幕
type MediaTranscription struct {
	models.BaseModel
	MediaId  string `db:"media_id" json:"media_id" pocketbase:"text"`
	From     int32  `db:"from" json:"from" pocketbase:"number"`
	To       int32  `db:"to" json:"to" pocketbase:"number"`
	Content  string `db:"content" json:"content" pocketbase:"text"`
	Keywords string `db:"keywords" json:"keywords" pocketbase:"text"`
	Speaker  string `db:"speaker" json:"speaker" pocketbase:"text"`
}

func (mt *MediaTranscription) TableName() string {
	return "media_transcriptions"
}

type PresetQuestion struct {
	models.BaseModel
	// Id       string `db:"id" json:"id" pocketbase:"text"`
	Question string `db:"question" json:"question" pocketbase:"text"`
	Prompt   string `db:"prompt" json:"prompt" pocketbase:"text"`
}

func (q *PresetQuestion) TableName() string {
	return "preset_questions"
}

type TaskQuestionAnswer struct {
	models.BaseModel
	TaskId  string `db:"task_id" json:"task_id" pocketbase:"text"`
	MediaId string `db:"media_id" json:"media_id" pocketbase:"text"` // 应该可以删除，因为可以通过任务表查询到对应的 media 是谁
	// 预设问题的Id
	PresetQuestionId string `db:"preset_question_id" json:"preset_question_id" pocketbase:"text"`
	// 预设问题
	Question string `db:"question" json:"question" pocketbase:"text"`
	// 预设本问题的答案
	Answer string `db:"answer" json:"answer" pocketbase:"text"`
}

func (mqa *TaskQuestionAnswer) TableName() string {
	return "task_question_answers"
}

type Device struct {
	models.BaseModel
	// DeviceName 设备名称
	DeviceName string `db:"device_name" json:"device_name" pocketbase:"text"`
	// DeviceID 设备id
	DeviceID string `db:"device_id" json:"device_id" pocketbase:"text"`
	// Online 在线状态
	Online bool `db:"online" json:"online" pocketbase:"bool"`
	// LastPingTime 最后心跳时间
	LastPingTime int64 `db:"last_ping_time" json:"last_ping_time" pocketbase:"number"`
}

func (device *Device) TableName() string {
	return "devices"
}
