package master

import (
	"fmt"
	"time"

	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master"
	"github.com/OmniOrigin/projects-jnqx-video-extract/api/master_internal"
	"github.com/OmniOrigin/projects-jnqx-video-extract/components/grpcstubs"
	"github.com/OmniOrigin/projects-jnqx-video-extract/components/httpx"
	"github.com/OmniOrigin/projects-jnqx-video-extract/config"
	"github.com/jony4/vclient"
	"github.com/pocketbase/dbx"
	"github.com/pocketbase/pocketbase"
	"github.com/pocketbase/pocketbase/core"
	"github.com/sashabaranov/go-openai"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type Master struct {
	config     *config.Config
	grpcServer *grpc.Server
	app        *pocketbase.PocketBase
	vclient    *vclient.Client
}

func NewService(cfg *config.Config, app *pocketbase.PocketBase) (*Master, error) {
	srv := &Master{
		config: cfg,
		app:    app,
	}
	// 将服务注册进 grpc server 中
	grpcServer := grpcstubs.GRPCServer()
	go func() {
		master.RegisterMasterServiceServer(grpcServer, srv)
		master_internal.RegisterMasterInternalServiceServer(grpcServer, srv)

		grpcstubs.GRPCServe(grpcServer, srv.config.GrpcServerEndpoint)

		grpcstubs.HTTPServe(srv.config.GrpcServerEndpoint,
			srv.config.HTTPServerEndpoint, master.RegisterMasterServiceHandlerFromEndpoint,
			map[string]grpcstubs.HandleFunc{
				"/v1/chat/completions": srv.ChatCompletions,
			},
		)
	}()
	srv.grpcServer = grpcServer

	app.OnBeforeServe().Add(func(e *core.ServeEvent) error {
		api := e.Router.Group("/v1/kbs")
		{
			api.POST("/datasets", srv.KBsCreate)
			api.GET("/datasets/:dataset_id", srv.KBsView)
			api.POST("/datasets/:dataset_id/upload", srv.KBsUpload)
			api.POST("/assistants", srv.KBsAssistantCreate)
			api.POST("/assistants/:assistant_id/chat/completions", srv.KBsChatCompletions)
		}
		return nil
	})

	go srv.CheckDevicesOnline()

	app.OnTerminate().Add(func(e *core.TerminateEvent) error {
		return srv.Close()
	})

	vclientCfg := &vclient.Config{
		Endpoints: map[vclient.ServiceConfigName]vclient.Endpoint{
			vclient.ServiceConfigNameRagflow: {
				Addresses: []string{cfg.RAGFlowEndpoint.String()},
			},
		},
	}

	vc, err := vclient.New(vclientCfg)
	if err != nil {
		return nil, fmt.Errorf("vclient.New(cfg) %v", err)
	}
	srv.vclient = vc

	return srv, nil
}

func (m *Master) ragflowOpenAIClient(assistantId string) (*openai.Client, error) {
	cfg := openai.DefaultConfig(m.config.RAGFlowEndpoint.Password)
	cfg.BaseURL = m.config.RAGFlowEndpoint.String() + "/api/v1/chats_openai/" + assistantId
	cfg.EmptyMessagesLimit = 10240
	return openai.NewClientWithConfig(cfg), nil
}

func (m *Master) openaiClient(model string) (*openai.Client, error) {
	switch model {
	case "chatglm3-32k":
		return openai.NewClientWithConfig(openai.ClientConfig{
			BaseURL:            m.config.LocalLLMEndpoint.Address,
			HTTPClient:         httpx.NewClientWithBasicAuth(m.config.LocalLLMEndpoint.UserName, m.config.LocalLLMEndpoint.Password),
			EmptyMessagesLimit: 10240,
		}), nil
	case "deepseek-chat", "deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1", "Pro/deepseek-ai/DeepSeek-R1", "Pro/deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-V2.5", "ep-20250218124100-v5ttj":
		cfg := openai.DefaultConfig(m.config.OnlineLLMEndpoint.Password)
		cfg.BaseURL = m.config.OnlineLLMEndpoint.Address
		cfg.EmptyMessagesLimit = 10240
		return openai.NewClientWithConfig(cfg), nil
	default:
		return openai.NewClientWithConfig(openai.ClientConfig{
			BaseURL:            m.config.LocalLLMEndpoint.Address,
			HTTPClient:         httpx.NewClientWithBasicAuth(m.config.LocalLLMEndpoint.UserName, m.config.LocalLLMEndpoint.Password),
			EmptyMessagesLimit: 10240,
		}), nil
	}
}

func (m *Master) Close() error {
	m.grpcServer.Stop()
	return nil
}

// CheckDevicesOnline 检查数据库中的所有标记在线的设备是否在线
func (m *Master) CheckDevicesOnline() {
	tick := time.NewTicker(1 * time.Minute)
	defer tick.Stop()

	for {
		select {
		case <-tick.C:
			var devices []*Device
			if err := m.app.Dao().ModelQuery(&Device{}).Where(dbx.HashExp{"online": true}).All(&devices); err != nil {
				log.Errorf("app.FindAll failed: %v", err)
				continue
			}
			for _, device := range devices {
				// 检查设备的 lastPingTime 是否已经超过1分钟
				if time.Now().Unix()-device.LastPingTime > 60 {
					// 如果超过1分钟，将设备的 online 状态更新为 false
					device.Online = false
					if err := m.app.Dao().Save(device); err != nil {
						log.Errorf("Save failed: %v", err)
					}
				}
			}
		}
	}
}
