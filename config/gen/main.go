package main

import (
	"bytes"
	"encoding/json"
	"os"
	"time"

	"github.com/OmniOrigin/projects-jnqx-video-extract/config"
)

func main() {
	var (
		pwd, _      = os.Getwd()
		serviceName = "JNQXVideoExtract"
	)

	// 初始化
	cfg := &config.Config{
		ServiceName:    serviceName,
		Debug:          true,
		LogLevel:       "debug",
		DefaultTimeout: 10 * time.Second,
		WorkDir:        "/Users/<USER>/go/src/github.com/OmniOrigin/projects-jnqx-video-extract/work_dir",
		MediaDir:       "/Users/<USER>/go/src/github.com/OmniOrigin/projects-jnqx-video-extract/media_dir",
		GrpcServerEndpoint: &config.Endpoint{
			Name:     "grpc",
			Address:  "0.0.0.0",
			Port:     8963,
			UserName: "",
			Password: "",
		},
		HTTPServerEndpoint: &config.Endpoint{
			Name:     "http",
			Address:  "0.0.0.0",
			Port:     8962,
			UserName: "",
			Password: "",
		},
		MasterEndpoint: &config.Endpoint{
			Name:     "master",
			Address:  "0.0.0.0",
			Port:     8973,
			UserName: "",
			Password: "",
		},
		DeviceId:       "e69e40b8-ebb9-6dd6-f3ef-833cd8cea084",
		MaxRunningJobs: 1,
		LocalLLMEndpoint: &config.Endpoint{
			Name:     "llm",
			Address:  "http://*************:9997/v1",
			Port:     0,
			UserName: "jony4",
			Password: "Stckf0QKEIF3bZjT",
		},
		OnlineLLMEndpoint: &config.Endpoint{
			Name:     "llm",
			Address:  "https://ark.cn-beijing.volces.com/api/v3",
			Port:     0,
			UserName: "",
			Password: "2ead7f64-4d49-4809-afae-fb0dd7a9f31a",
		},
		// OnlineLLMEndpoint: &config.Endpoint{
		// 	Name:     "llm",
		// 	Address:  "https://api.deepseek.com/beta",
		// 	Port:     0,
		// 	UserName: "",
		// 	Password: "sk-cb83a963810743d680f3280cf1fd827a",
		// },
		FunasrEndpoint: &config.Endpoint{
			Name:     "funasr",
			Address:  "http://*************",
			Port:     8001,
			UserName: "",
			Password: "",
		},
		WhisperModel: "ggml-tiny.bin",
		Models: &config.Models{
			LLM:          "chatglm3-32k",
			LLMMaxTokens: 8 * 1024,
			Embedding:    "bce-embedding-base_v1",
			ReRanker:     "bce-reranker-base_v1",
			Audio:        "Belle-whisper-large-v2-zh",
			CurrentModel: "ep-20250218124100-v5ttj",
		},
		RAGFlowEndpoint: &config.Endpoint{
			Name:     "ragflow",
			Address:  "http://**********",
			Port:     9380,
			UserName: "",
			Password: "ragflow-ZkNWFhZDRhMmY4ZTExZjA5NjkxMDI0Mm",
		},
	}
	cfgBytes, err := json.Marshal(cfg)
	if err != nil {
		panic(err)
	}
	var out bytes.Buffer
	json.Indent(&out, cfgBytes, "", "\t")
	os.WriteFile(pwd+"/config_master.json", out.Bytes(), 0755)
	os.WriteFile(pwd+"/config_worker.json", out.Bytes(), 0755)

	// 生产
	cfg.LocalLLMEndpoint.Address = "http://127.0.0.1:9997/v1"
	cfg.WorkDir = "/apps/data/work_dir"
	cfg.MediaDir = "/apps/data/media_dir"
	cfg.FunasrEndpoint.Address = "http://common_asr"
	cfg.FunasrEndpoint.Port = 8000

	// 生产 master_upgrade
	// cfg.Debug = false
	cfgProdMasterUpgradeBytes, err := json.Marshal(cfg)
	if err != nil {
		panic(err)
	}
	var outProdMasterUpgrade bytes.Buffer
	json.Indent(&outProdMasterUpgrade, cfgProdMasterUpgradeBytes, "", "\t")
	os.WriteFile(pwd+"/config_prod_master_upgrade.json", outProdMasterUpgrade.Bytes(), 0755)

	// 生产 worker_upgrade
	cfg.MasterEndpoint.Address = "master_upgrade"
	cfgProdWorkerUpgradeBytes, err := json.Marshal(cfg)
	if err != nil {
		panic(err)
	}
	var outProdWorkerUpgrade bytes.Buffer
	json.Indent(&outProdWorkerUpgrade, cfgProdWorkerUpgradeBytes, "", "\t")
	os.WriteFile(pwd+"/config_prod_worker_upgrade.json", outProdWorkerUpgrade.Bytes(), 0755)

	cfgMPB := &config.Config{
		ServiceName:    serviceName,
		Debug:          true,
		LogLevel:       "debug",
		DefaultTimeout: 10 * time.Second,
		WorkDir:        "/Users/<USER>/go/src/github.com/OmniOrigin/projects-jnqx-video-extract/work_dir",
		MediaDir:       "/Users/<USER>/go/src/github.com/OmniOrigin/projects-jnqx-video-extract/media_dir",
		GrpcServerEndpoint: &config.Endpoint{
			Name:     "grpc",
			Address:  "0.0.0.0",
			Port:     8963,
			UserName: "",
			Password: "",
		},
		HTTPServerEndpoint: &config.Endpoint{
			Name:     "http",
			Address:  "0.0.0.0",
			Port:     8962,
			UserName: "",
			Password: "",
		},
		MasterEndpoint: &config.Endpoint{
			Name:     "master",
			Address:  "*************",
			Port:     8973,
			UserName: "",
			Password: "",
		},
		DeviceId:       "e69e40b8-ebb9-6dd6-f3ef-833cd8cea084",
		MaxRunningJobs: 1,
		LocalLLMEndpoint: &config.Endpoint{
			Name:     "llm",
			Address:  "http://*************:9997/v1",
			Port:     0,
			UserName: "jony4",
			Password: "Stckf0QKEIF3bZjT",
		},
		OnlineLLMEndpoint: &config.Endpoint{
			Name:     "llm",
			Address:  "https://ark.cn-beijing.volces.com/api/v3",
			Port:     0,
			UserName: "",
			Password: "2ead7f64-4d49-4809-afae-fb0dd7a9f31a",
		},
		FunasrEndpoint: &config.Endpoint{
			Name:     "funasr",
			Address:  "http://*************",
			Port:     8001,
			UserName: "",
			Password: "",
		},
		WhisperModel: "ggml-tiny.bin",
		Models: &config.Models{
			LLM:          "chatglm3-32k",
			LLMMaxTokens: 8 * 1024,
			Embedding:    "bce-embedding-base_v1",
			ReRanker:     "bce-reranker-base_v1",
			Audio:        "Belle-whisper-large-v2-zh",
			CurrentModel: "ep-20250218124100-v5ttj",
		},
		RAGFlowEndpoint: &config.Endpoint{
			Name:     "ragflow",
			Address:  "http://*************",
			Port:     9380,
			UserName: "",
			Password: "ragflow-MzMWZiNzUwMmJkZTExZjA5YTg0YzY2NW",
		},
	}

	// 本地 worker
	cfgMBPMasterBytes, err := json.Marshal(cfgMPB)
	if err != nil {
		panic(err)
	}
	var outLocalWorker bytes.Buffer
	json.Indent(&outLocalWorker, cfgMBPMasterBytes, "", "\t")
	os.WriteFile(pwd+"/config_mbp_master.json", outLocalWorker.Bytes(), 0755)

	// 本地 worker
	cfgMBPWorkerBytes, err := json.Marshal(cfgMPB)
	if err != nil {
		panic(err)
	}
	var outWorker bytes.Buffer
	json.Indent(&outWorker, cfgMBPWorkerBytes, "", "\t")
	os.WriteFile(pwd+"/config_mbp_worker.json", outWorker.Bytes(), 0755)
}
