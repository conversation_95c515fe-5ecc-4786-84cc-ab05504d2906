package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

type Config struct {
	// 基础配置
	ServiceName    string        `json:"service_name"`
	Debug          bool          `json:"debug"`
	LogLevel       string        `json:"log_level"`
	DefaultTimeout time.Duration `json:"default_timeout"`
	WorkDir        string        `json:"work_dir"`
	MediaDir       string        `json:"media_dir"`
	// 外部依赖服务的 endpoint
	GrpcServerEndpoint *Endpoint `json:"grpc_server_endpoint"`
	HTTPServerEndpoint *Endpoint `json:"http_server_endpoint"`
	// Minio Config
	// Minio *Minio `json:"minio"`
	// worker config
	MasterEndpoint    *Endpoint `json:"master_endpoint"`
	DeviceId          string    `json:"device_id"`
	MaxRunningJobs    int32     `json:"max_running_jobs"`
	LocalLLMEndpoint  *Endpoint `json:"local_llm_endpoint"`
	OnlineLLMEndpoint *Endpoint `json:"online_llm_endpoint"`
	FunasrEndpoint    *Endpoint `json:"funasr_endpoint"`
	WhisperModel      string    `json:"whisper_model"`
	Models            *Models   `json:"models"`
	RAGFlowEndpoint   *Endpoint `json:"ragflow_endpoint"`
}

// LoadFromJSONFile JSON文件解析
func LoadFromJSONFile(path string) (*Config, error) {
	var cfg Config
	content, err := os.ReadFile(filepath.Clean(path))
	if err != nil {
		return nil, fmt.Errorf("read config file failed: %v", err)
	}
	if err = json.Unmarshal(content, &cfg); err != nil {
		return nil, fmt.Errorf("unmarshal json failed: %v", err)
	}
	return &cfg, nil
}

// Clone

type Models struct {
	LLM          string `json:"llm"`
	LLMMaxTokens int    `json:"llm_max_tokens"`
	Embedding    string `json:"embedding"`
	ReRanker     string `json:"reranker"`
	Audio        string `json:"audio"`
	CurrentModel string `json:"current_model"`
}

type Minio struct {
	Endpoint        string `json:"endpoint"`
	AccessKeyID     string `json:"access"`
	SecretAccessKey string `json:"secret"`
	UseSSL          bool   `json:"use_ssl"`
	BucketName      string `json:"bucket_name"`
}

type DB struct {
	Driver string `json:"driver"`
	DSN    string `json:"dsn"`
}

type RedisServer struct {
	Address  string `json:"address"`
	Password string `json:"password"`
}

// Endpoint 相关配置
type Endpoint struct {
	Name     string `json:"name"`
	Address  string `json:"address"`
	Port     uint16 `json:"port"`
	UserName string `json:"username"`
	Password string `json:"password"`
}

// Endpoint string 格式
func (e *Endpoint) String() string {
	return fmt.Sprintf("%v:%v", e.Address, e.Port)
}
